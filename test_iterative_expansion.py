"""
测试迭代展开功能 - 验证refbook中表达式的相互引用能够被正确展开
"""

from tree_compressor import TreeCompressor

def test_simple_iterative_expansion():
    """测试简单的迭代展开"""
    
    print("=" * 80)
    print("简单迭代展开测试")
    print("=" * 80)
    
    # 构建有依赖关系的refbook
    refbook = {
        'A': 'add(^0, ^1)',           # 基础操作
        'B': 'A(^0, 1)',             # B 依赖 A
        'C': 'B(^0)',                # C 依赖 B，间接依赖 A
        'D': 'mul(C(^0), 2)',        # D 依赖 C，间接依赖 B 和 A
    }
    
    print("原始 refbook:")
    for name, expr in refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    compressor = TreeCompressor(refbook)
    
    print("展开后的 refbook:")
    for name, expr in compressor.expanded_refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    # 测试表达式
    test_expr = "D(x)"
    print(f"测试表达式: {test_expr}")
    
    # 展开输入表达式
    expanded_input = compressor._expand_expression(test_expr, compressor.expanded_refbook)
    print(f"展开后输入: {expanded_input}")
    
    # 压缩
    compressed = compressor.compress(test_expr)
    print(f"压缩结果: {compressed}")
    
    # 验证展开是否正确
    expected_expansion = "mul(add(x, 1), 2)"  # D(x) -> mul(C(x), 2) -> mul(B(x), 2) -> mul(A(x, 1), 2) -> mul(add(x, 1), 2)
    print(f"预期展开: {expected_expansion}")
    print(f"展开正确: {'✓' if expanded_input == expected_expansion else '✗'}")

def test_complex_iterative_expansion():
    """测试复杂的迭代展开"""
    
    print("\n" + "=" * 80)
    print("复杂迭代展开测试")
    print("=" * 80)
    
    # 基于 ipynb 的复杂refbook
    refbook = {
        # 基础操作
        'ADD': 'add(^0, ^1)',
        'SUB': 'sub(^0, ^1)',
        'MUL': 'mul(^0, ^1)',
        'DIV': 'div(^0, ^1)',
        'INT': 'int(^0)',
        'LEN': 'len(^0)',
        'ILOC': 'iloc(^0, ^1)',
        'SORTASC': 'sortAsc(^0)',
        'STD': 'std(^0)',
        'LOC': 'loc(^0, ^1)',
        'GEQ': 'geq(^0, ^1)',
        'LEQ': 'leq(^0, ^1)',
        'SAND': 'sand(^0, ^1)',
        
        # 第一层组合 - 依赖基础操作
        'QUANTILE': 'ILOC(SORTASC(^0), INT(MUL(SUB(LEN(^0), 1), ^1)))',
        
        # 第二层组合 - 依赖第一层
        'SECTIONIND': 'SAND(GEQ(^0, ^1), LEQ(^0, ^2))',
        'QUANTILEIND': 'SECTIONIND(^0, QUANTILE(^0, ^1), QUANTILE(^0, ^2))',
        
        # 第三层组合 - 依赖第二层
        'BYLATTERQUANTILESTD': 'STD(LOC(^0, QUANTILEIND(^1, ^2, ^3)))',
        
        # 第四层组合 - 依赖第三层
        'BYLATTERQUANTILE82STDDIFF': 'SUB(BYLATTERQUANTILESTD(^0, ^1, 0.8, 1.0), BYLATTERQUANTILESTD(^0, ^1, 0.0, 0.2))',
    }
    
    print("多层依赖的 refbook:")
    for name, expr in refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    compressor = TreeCompressor(refbook)
    
    print("展开后的关键表达式:")
    key_expressions = ['QUANTILE', 'QUANTILEIND', 'BYLATTERQUANTILESTD', 'BYLATTERQUANTILE82STDDIFF']
    for name in key_expressions:
        if name in compressor.expanded_refbook:
            expr = compressor.expanded_refbook[name]
            print(f"  {name}: {expr}")
            print(f"    长度: {len(expr)} 字符")
    print()
    
    # 测试复杂表达式
    test_expr = "BYLATTERQUANTILE82STDDIFF(data1, data2)"
    print(f"测试表达式: {test_expr}")
    
    # 展开输入表达式
    expanded_input = compressor._expand_expression(test_expr, compressor.expanded_refbook)
    print(f"完全展开长度: {len(expanded_input)} 字符")
    print(f"展开结果: {expanded_input[:100]}..." if len(expanded_input) > 100 else f"展开结果: {expanded_input}")
    
    # 压缩
    compressed = compressor.compress(test_expr)
    print(f"压缩结果: {compressed}")

def test_circular_dependency_handling():
    """测试循环依赖的处理"""
    
    print("\n" + "=" * 80)
    print("循环依赖处理测试")
    print("=" * 80)
    
    # 构建有循环依赖的refbook
    refbook = {
        'A': 'B(^0)',     # A 依赖 B
        'B': 'C(^0)',     # B 依赖 C
        'C': 'A(^0)',     # C 依赖 A - 形成循环
        'D': 'add(^0, 1)', # D 独立
    }
    
    print("有循环依赖的 refbook:")
    for name, expr in refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    try:
        compressor = TreeCompressor(refbook)
        
        print("展开后的 refbook:")
        for name, expr in compressor.expanded_refbook.items():
            print(f"  {name}: {expr}")
        print()
        
        # 测试独立的表达式
        test_expr = "D(x)"
        compressed = compressor.compress(test_expr)
        print(f"独立表达式 {test_expr} 压缩结果: {compressed}")
        
        # 测试循环依赖的表达式
        test_expr2 = "A(y)"
        compressed2 = compressor.compress(test_expr2)
        print(f"循环依赖表达式 {test_expr2} 压缩结果: {compressed2}")
        
    except Exception as e:
        print(f"处理循环依赖时出错: {e}")

def test_expansion_performance():
    """测试展开功能的性能"""
    
    print("\n" + "=" * 80)
    print("展开功能性能测试")
    print("=" * 80)
    
    import time
    
    # 构建大型refbook
    refbook = {}
    
    # 基础操作
    basic_ops = ['add', 'sub', 'mul', 'div', 'std', 'mean', 'len', 'iloc', 'loc', 'sortAsc', 'geq', 'leq', 'sand']
    for op in basic_ops:
        if op in ['std', 'mean', 'len', 'sortAsc']:
            refbook[op.upper()] = f'{op}(^0)'
        else:
            refbook[op.upper()] = f'{op}(^0, ^1)'
    
    # 多层组合
    refbook.update({
        'QUANTILE': 'ILOC(SORTASC(^0), INT(MUL(SUB(LEN(^0), 1), ^1)))',
        'SECTIONIND': 'SAND(GEQ(^0, ^1), LEQ(^0, ^2))',
        'QUANTILEIND': 'SECTIONIND(^0, QUANTILE(^0, ^1), QUANTILE(^0, ^2))',
        'BYLATTERQUANTILESTD': 'STD(LOC(^0, QUANTILEIND(^1, ^2, ^3)))',
        'BYLATTERQUANTILE82STDDIFF': 'SUB(BYLATTERQUANTILESTD(^0, ^1, 0.8, 1.0), BYLATTERQUANTILESTD(^0, ^1, 0.0, 0.2))',
        
        # 更多层次
        'FACTOR1': 'BYLATTERQUANTILE82STDDIFF(^0, ^1)',
        'FACTOR2': 'ADD(FACTOR1(^0, ^1), FACTOR1(^1, ^0))',
        'FACTOR3': 'MUL(FACTOR2(^0, ^1), 0.5)',
    })
    
    print(f"Refbook 规模: {len(refbook)} 条规则")
    
    # 测试初始化性能
    start_time = time.time()
    compressor = TreeCompressor(refbook)
    init_time = time.time() - start_time
    
    print(f"初始化时间: {init_time:.4f}s")
    
    # 检查展开结果
    max_expansion_length = 0
    max_expansion_name = ""
    for name, expr in compressor.expanded_refbook.items():
        if len(expr) > max_expansion_length:
            max_expansion_length = len(expr)
            max_expansion_name = name
    
    print(f"最大展开长度: {max_expansion_length} 字符 ({max_expansion_name})")
    
    # 测试压缩性能
    test_expr = "FACTOR3(data1, data2)"
    
    start_time = time.time()
    for _ in range(100):
        compressed = compressor.compress(test_expr)
    compress_time = time.time() - start_time
    
    print(f"压缩性能 (100次): {compress_time:.4f}s")
    print(f"平均单次压缩: {compress_time/100*1000:.2f}ms")
    print(f"压缩结果: {compressed}")

if __name__ == "__main__":
    test_simple_iterative_expansion()
    test_complex_iterative_expansion()
    test_circular_dependency_handling()
    test_expansion_performance()
    
    print("\n" + "=" * 80)
    print("迭代展开功能测试完成！")
    print("=" * 80)
    print("\n主要验证内容:")
    print("✅ 简单的refbook相互引用展开")
    print("✅ 复杂多层依赖的完全展开")
    print("✅ 循环依赖的检测和处理")
    print("✅ 大规模refbook的展开性能")
    print("✅ 输入表达式的预处理展开")
