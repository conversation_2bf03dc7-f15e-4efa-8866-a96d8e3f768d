from numba import jit
from copy import copy

# 按括号和逗号劈函数和输入
def split_bracket(expr):
    parts = expr.split('(')
    return [parts[0], split_comma('('.join(parts[1:])[:-1])]

@jit
def split_comma(expr):
    res = []
    bra_count = 0
    curr_str = ''
    for x in expr:
        if x==',' and bra_count == 0:
            res.append(curr_str)
            curr_str = ''
        else:
            curr_str += x
            if x == '(':
                bra_count += 1
            elif x == ')':
                bra_count -= 1
    res.append(curr_str)
    return res

# 再命名映射(树状递归)
def rename(expr):
    if '(' not in expr:
        return expr
    parts = split_bracket(expr)
    if parts[0] not in DEFBOOK.keys():
        return '%s(%s)'% (parts[0], ','.join([rename(arg) for arg in parts[1]]))
    else:
        expand = copy(DEFBOOK[parts[0]])
        n_args = len(parts[1])
        for i in range(n_args):
            expand = expand.replace('^%s'%i, parts[1][i])
        return rename(expand)

# 基础算符
def add(x,y): return x+y
def sub(x,y): return x-y
def mul(x,y): return x*y
def div(x,y): return x/y
def pow(x,y): return x**y
def abs(x): return np.abs(x)
def sum(x): return x.sum()
def mean(x): return x.mean()
def std(x): return x.std()
def skew(x): return x.skew()
def kurt(x): return x.kurt()
def sortAsc(x): return x.sort_values(ascending=True)
def sortDec(x): return x.sort_values(ascending=False)
def fillna(x,y): return x.fillna(y)
def geq(x,y): return x>=y
def leq(x,y): return x<=y
def gt(x,y): return x>y
def lt(x,y): return x<y
def loc(x,y): return x.loc[y]
def iloc(x,y): return x.iloc[y]
def eq(x,y): return x==y
def ne(x,y): return x!=y
def sand(x,y): return x&y
def sor(x,y): return x|y
#len(x) exists
#T(x) exists for T a Python type

# 再命名字典
DEFBOOK = {
    'quantile': 'iloc(sortAsc(^0),int(mul(sub(len(^0),1),^1)))', # 计算序列 ^0 的 ^1 分位数
    'sectionInd': 'sand(geq(^0,^1),leq(^0,^2))', # 计算序列 ^0 值 >=^1 且 <=^2 的布尔值
    'quantileInd': 'sectionInd(^0,quantile(^0,^1),quantile(^0,^2))', # 计算序列 ^0 值 >=^0的^1分位 且 <=^0的^2分位 的布尔值
    'byLatterQuantileStd': 'std(loc(^0,quantileInd(^1,^2,^3)))', # 计算序列 ^0 值在 ^1位于其自身^2到^3分位 位置的标准差
    'byLatterQuantile82StdDiff': 'sub(byLatterQuantileStd(^0,^1,0.8,1.0),byLatterQuantileStd(^0,^1,0.0,0.2))',
    # 计算 序列^0 分别在 序列^1 在其自身的 0.8-1.0分位 以及 0.0-0.2分位 位置的标准差的减差
 }

data1 = pd.Series(np.random.normal(0,1,1000))
data2 = pd.Series(np.random.normal(0,1,1000))

factor_expr = 'byLatterQuantile82StdDiff(data1,data2)'

syntactic_expr = rename(factor_expr)

print(syntactic_expr)

res = eval(syntactic_expr)

res

sub(std(loc(data1,sand(geq(data2,iloc(sortAsc(data2),int(mul(sub(len(data2),1),0.8)))),leq(data2,iloc(sortAsc(data2),int(mul(sub(len(data2),1),1.0))))))),std(loc(data1,sand(geq(data2,iloc(sortAsc(data2),int(mul(sub(len(data2),1),0.0)))),leq(data2,iloc(sortAsc(data2),int(mul(sub(len(data2),1),0.2))))))))



