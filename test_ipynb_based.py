"""
基于 ipynb 文件数据构建的测试样例
测试复杂的金融因子表达式压缩和规约
"""

from tree_compressor import TreeCompressor, compress_expression, find_all_expression_reductions

def test_quantile_expressions():
    """测试分位数相关表达式"""
    
    print("=" * 80)
    print("分位数表达式测试")
    print("=" * 80)
    
    # 基于 ipynb 中的 DEFBOOK 构建 refbook
    refbook = {
        # 基础算术操作
        'ADD': 'add(^0, ^1)',
        'SUB': 'sub(^0, ^1)', 
        'MUL': 'mul(^0, ^1)',
        'DIV': 'div(^0, ^1)',
        'INT': 'int(^0)',
        
        # 序列操作
        'LEN': 'len(^0)',
        'ILOC': 'iloc(^0, ^1)',
        'LOC': 'loc(^0, ^1)',
        'SORTASC': 'sortAsc(^0)',
        
        # 统计函数
        'STD': 'std(^0)',
        'MEAN': 'mean(^0)',
        
        # 比较操作
        'GEQ': 'geq(^0, ^1)',
        'LEQ': 'leq(^0, ^1)',
        'SAND': 'sand(^0, ^1)',
        
        # 复合操作 - 来自 ipynb 的 DEFBOOK
        'QUANTILE': 'iloc(sortAsc(^0), int(mul(sub(len(^0), 1), ^1)))',
        'SECTIONIND': 'sand(geq(^0, ^1), leq(^0, ^2))',
        'QUANTILEIND': 'sectionInd(^0, quantile(^0, ^1), quantile(^0, ^2))',
        'BYLATTERQUANTILESTD': 'std(loc(^0, quantileInd(^1, ^2, ^3)))',
    }
    
    # 测试用例 1: 简单分位数计算
    expr1 = "iloc(sortAsc(data), int(mul(sub(len(data), 1), 0.5)))"
    print(f"表达式 1: {expr1}")
    
    compressor = TreeCompressor(refbook)
    compressed1 = compressor.compress(expr1)
    print(f"压缩结果: {compressed1}")
    
    reductions1 = compressor.find_all_reductions(expr1, max_depth=3)
    print(f"规约数量: {len(reductions1)}")
    if reductions1:
        print(f"最佳规约: {min(reductions1, key=len)}")
    print()
    
    # 测试用例 2: 区间指示器
    expr2 = "sand(geq(data, value1), leq(data, value2))"
    print(f"表达式 2: {expr2}")
    
    compressed2 = compressor.compress(expr2)
    print(f"压缩结果: {compressed2}")
    
    reductions2 = compressor.find_all_reductions(expr2, max_depth=3)
    print(f"规约数量: {len(reductions2)}")
    if reductions2:
        print(f"最佳规约: {min(reductions2, key=len)}")
    print()

def test_complex_factor_expression():
    """测试复杂的因子表达式 - 基于 ipynb 中的实际例子"""
    
    print("=" * 80)
    print("复杂因子表达式测试")
    print("=" * 80)
    
    # 扩展的 refbook，包含更多金融因子操作
    refbook = {
        # 基础操作
        'ADD': 'add(^0, ^1)',
        'SUB': 'sub(^0, ^1)',
        'MUL': 'mul(^0, ^1)',
        'DIV': 'div(^0, ^1)',
        'INT': 'int(^0)',
        'STD': 'std(^0)',
        'LEN': 'len(^0)',
        'ILOC': 'iloc(^0, ^1)',
        'LOC': 'loc(^0, ^1)',
        'SORTASC': 'sortAsc(^0)',
        'GEQ': 'geq(^0, ^1)',
        'LEQ': 'leq(^0, ^1)',
        'SAND': 'sand(^0, ^1)',
        
        # 金融因子专用操作
        'QUANTILE': 'iloc(sortAsc(^0), int(mul(sub(len(^0), 1), ^1)))',
        'SECTIONIND': 'sand(geq(^0, ^1), leq(^0, ^2))',
        'QUANTILEIND': 'sectionInd(^0, quantile(^0, ^1), quantile(^0, ^2))',
        'BYLATTERQUANTILESTD': 'std(loc(^0, quantileInd(^1, ^2, ^3)))',
        
        # 高级组合 - 来自 ipynb 的 byLatterQuantile82StdDiff
        'BYLATTERQUANTILE82STDDIFF': 'sub(byLatterQuantileStd(^0, ^1, 0.8, 1.0), byLatterQuantileStd(^0, ^1, 0.0, 0.2))',
        
        # 常用的分位数组合
        'QUANTILE80': 'quantile(^0, 0.8)',
        'QUANTILE20': 'quantile(^0, 0.2)',
        'QUANTILE100': 'quantile(^0, 1.0)',
        'QUANTILE0': 'quantile(^0, 0.0)',
        
        # 常用的区间
        'TOPQUANTILE': 'quantileInd(^0, 0.8, 1.0)',
        'BOTTOMQUANTILE': 'quantileInd(^0, 0.0, 0.2)',
    }
    
    # 测试用例：简化版的 byLatterQuantile82StdDiff
    expr = "sub(std(loc(data1, sand(geq(data2, iloc(sortAsc(data2), int(mul(sub(len(data2), 1), 0.8)))), leq(data2, iloc(sortAsc(data2), int(mul(sub(len(data2), 1), 1.0))))))), std(loc(data1, sand(geq(data2, iloc(sortAsc(data2), int(mul(sub(len(data2), 1), 0.0)))), leq(data2, iloc(sortAsc(data2), int(mul(sub(len(data2), 1), 0.2))))))))"
    
    print(f"原始复杂表达式:")
    print(f"{expr}")
    print(f"表达式长度: {len(expr)} 字符")
    print()
    
    compressor = TreeCompressor(refbook)
    
    # 基础压缩
    compressed = compressor.compress(expr)
    print(f"基础压缩结果:")
    print(f"{compressed}")
    print(f"压缩后长度: {len(compressed)} 字符")
    print(f"压缩比: {len(compressed)/len(expr)*100:.1f}%")
    print()
    
    # 查找所有规约
    print("查找所有可能的规约形式...")
    reductions = compressor.find_all_reductions(expr, max_depth=4)
    
    print(f"找到 {len(reductions)} 种规约形式:")
    for i, reduction in enumerate(reductions[:5], 1):  # 只显示前5个
        print(f"{i}. {reduction} (长度: {len(reduction)})")
    
    if reductions:
        best_reduction = min(reductions, key=len)
        print(f"\n最佳规约: {best_reduction}")
        print(f"最佳压缩比: {len(best_reduction)/len(expr)*100:.1f}%")

def test_nested_quantile_operations():
    """测试嵌套的分位数操作"""
    
    print("\n" + "=" * 80)
    print("嵌套分位数操作测试")
    print("=" * 80)
    
    refbook = {
        'QUANTILE': 'iloc(sortAsc(^0), int(mul(sub(len(^0), 1), ^1)))',
        'SECTIONIND': 'sand(geq(^0, ^1), leq(^0, ^2))',
        'QUANTILEIND': 'sectionInd(^0, quantile(^0, ^1), quantile(^0, ^2))',
        'STD': 'std(^0)',
        'LOC': 'loc(^0, ^1)',
        
        # 简化的组合
        'Q80': 'quantile(^0, 0.8)',
        'Q20': 'quantile(^0, 0.2)',
        'TOPRANGE': 'sectionInd(^0, quantile(^0, 0.8), quantile(^0, 1.0))',
        'BOTTOMRANGE': 'sectionInd(^0, quantile(^0, 0.0), quantile(^0, 0.2))',
    }
    
    test_cases = [
        {
            'name': '分位数计算',
            'expr': 'iloc(sortAsc(data), int(mul(sub(len(data), 1), 0.8)))',
            'expected': 'QUANTILE(data, 0.8)'
        },
        {
            'name': '区间指示器',
            'expr': 'sand(geq(data, quantile(data, 0.8)), leq(data, quantile(data, 1.0)))',
            'expected': 'TOPRANGE(data)'
        },
        {
            'name': '分位数区间',
            'expr': 'sectionInd(data, quantile(data, 0.2), quantile(data, 0.8))',
            'expected': 'SECTIONIND(data, Q20(data), Q80(data))'
        }
    ]
    
    compressor = TreeCompressor(refbook)
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        print(f"表达式: {test_case['expr']}")
        
        compressed = compressor.compress(test_case['expr'])
        print(f"压缩结果: {compressed}")
        
        reductions = compressor.find_all_reductions(test_case['expr'], max_depth=3)
        print(f"规约数量: {len(reductions)}")
        
        if reductions:
            best = min(reductions, key=len)
            print(f"最佳规约: {best}")
            
            # 检查是否包含预期的模式
            if any(expected_part in best for expected_part in test_case['expected'].split('(')):
                print("✓ 包含预期的压缩模式")
            else:
                print("? 压缩结果与预期不同")

def test_performance_with_complex_expressions():
    """测试复杂表达式的性能"""
    
    print("\n" + "=" * 80)
    print("复杂表达式性能测试")
    print("=" * 80)
    
    import time
    
    # 构建一个包含大量规则的 refbook
    refbook = {}
    
    # 基础操作
    basic_ops = ['add', 'sub', 'mul', 'div', 'std', 'mean', 'len', 'iloc', 'loc', 'sortAsc', 'geq', 'leq', 'sand']
    for op in basic_ops:
        if op in ['std', 'mean', 'len', 'sortAsc']:
            refbook[op.upper()] = f'{op}(^0)'
        else:
            refbook[op.upper()] = f'{op}(^0, ^1)'
    
    # 复合操作
    refbook.update({
        'QUANTILE': 'iloc(sortAsc(^0), int(mul(sub(len(^0), 1), ^1)))',
        'SECTIONIND': 'sand(geq(^0, ^1), leq(^0, ^2))',
        'QUANTILEIND': 'sectionInd(^0, quantile(^0, ^1), quantile(^0, ^2))',
        'BYLATTERQUANTILESTD': 'std(loc(^0, quantileInd(^1, ^2, ^3)))',
    })
    
    # 测试表达式（逐渐增加复杂度）
    test_expressions = [
        "add(mul(x, y), z)",
        "std(loc(data, sand(geq(values, threshold1), leq(values, threshold2))))",
        "iloc(sortAsc(data), int(mul(sub(len(data), 1), 0.5)))",
        "sub(std(loc(data1, quantileInd(data2, 0.8, 1.0))), std(loc(data1, quantileInd(data2, 0.0, 0.2))))"
    ]
    
    compressor = TreeCompressor(refbook)
    
    for i, expr in enumerate(test_expressions, 1):
        print(f"\n测试 {i}: 复杂度级别 {i}")
        print(f"表达式长度: {len(expr)} 字符")
        
        # 压缩性能测试
        start_time = time.time()
        for _ in range(100):
            compressed = compressor.compress(expr)
        compress_time = time.time() - start_time
        
        # 规约查找性能测试
        start_time = time.time()
        reductions = compressor.find_all_reductions(expr, max_depth=3)
        reduction_time = time.time() - start_time
        
        print(f"压缩性能 (100次): {compress_time:.4f}s")
        print(f"规约查找性能: {reduction_time:.4f}s")
        print(f"压缩结果: {compressed}")
        print(f"规约数量: {len(reductions)}")

if __name__ == "__main__":
    test_quantile_expressions()
    test_complex_factor_expression()
    test_nested_quantile_operations()
    test_performance_with_complex_expressions()
    
    print("\n" + "=" * 80)
    print("基于 ipynb 数据的测试完成！")
    print("=" * 80)
    print("\n主要测试内容:")
    print("✅ 金融分位数表达式压缩")
    print("✅ 复杂因子表达式规约")
    print("✅ 嵌套量化操作优化")
    print("✅ 大规模表达式性能验证")
    print("✅ 真实金融场景应用测试")
