"""
简化的迭代展开测试 - 验证refbook展开功能
"""

from tree_compressor import TreeCompressor

def test_basic_expansion():
    """测试基础的迭代展开"""
    
    print("=" * 60)
    print("基础迭代展开测试")
    print("=" * 60)
    
    # 简单的依赖关系
    refbook = {
        'A': 'add(^0, ^1)',
        'B': 'A(^0, 1)',
        'C': 'B(^0)',
    }
    
    print("原始 refbook:")
    for name, expr in refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    compressor = TreeCompressor(refbook)
    
    print("展开后的 refbook:")
    for name, expr in compressor.expanded_refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    # 测试表达式
    test_expr = "C(x)"
    print(f"测试表达式: {test_expr}")
    
    # 展开输入表达式
    expanded_input = compressor._expand_expression(test_expr, compressor.expanded_refbook)
    print(f"展开后输入: {expanded_input}")
    
    # 压缩
    compressed = compressor.compress(test_expr)
    print(f"压缩结果: {compressed}")

def test_no_dependencies():
    """测试没有依赖的情况"""
    
    print("\n" + "=" * 60)
    print("无依赖测试")
    print("=" * 60)
    
    refbook = {
        'ADD': 'add(^0, ^1)',
        'MUL': 'mul(^0, ^1)',
        'SUB': 'sub(^0, ^1)',
    }
    
    print("无依赖的 refbook:")
    for name, expr in refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    compressor = TreeCompressor(refbook)
    
    print("展开后的 refbook (应该保持不变):")
    for name, expr in compressor.expanded_refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    # 测试表达式
    test_expr = "ADD(MUL(x, y), z)"
    print(f"测试表达式: {test_expr}")
    
    expanded_input = compressor._expand_expression(test_expr, compressor.expanded_refbook)
    print(f"展开后输入: {expanded_input}")
    
    compressed = compressor.compress(test_expr)
    print(f"压缩结果: {compressed}")

def test_complex_dependencies():
    """测试复杂依赖"""
    
    print("\n" + "=" * 60)
    print("复杂依赖测试")
    print("=" * 60)
    
    refbook = {
        # 基础操作
        'add': 'add(^0, ^1)',
        'mul': 'mul(^0, ^1)',
        'sub': 'sub(^0, ^1)',
        'len': 'len(^0)',
        
        # 第一层组合
        'increment': 'add(^0, 1)',
        'double': 'mul(^0, 2)',
        
        # 第二层组合
        'inc_double': 'double(increment(^0))',
        
        # 第三层组合
        'complex_op': 'add(inc_double(^0), len(^1))',
    }
    
    print("复杂依赖的 refbook:")
    for name, expr in refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    compressor = TreeCompressor(refbook)
    
    print("展开后的关键表达式:")
    key_names = ['increment', 'inc_double', 'complex_op']
    for name in key_names:
        if name in compressor.expanded_refbook:
            expr = compressor.expanded_refbook[name]
            print(f"  {name}: {expr}")
    print()
    
    # 测试表达式
    test_expr = "complex_op(x, data)"
    print(f"测试表达式: {test_expr}")
    
    expanded_input = compressor._expand_expression(test_expr, compressor.expanded_refbook)
    print(f"展开后输入: {expanded_input}")
    
    compressed = compressor.compress(test_expr)
    print(f"压缩结果: {compressed}")

def test_performance():
    """测试性能"""
    
    print("\n" + "=" * 60)
    print("性能测试")
    print("=" * 60)
    
    import time
    
    # 构建中等规模的refbook
    refbook = {
        'add': 'add(^0, ^1)',
        'mul': 'mul(^0, ^1)',
        'sub': 'sub(^0, ^1)',
        'div': 'div(^0, ^1)',
        'len': 'len(^0)',
        'mean': 'mean(^0)',
        'std': 'std(^0)',
        
        # 组合操作
        'zscore': 'div(sub(^0, mean(^0)), std(^0))',
        'normalize': 'div(sub(^0, min(^0)), sub(max(^0), min(^0)))',
        'weighted_avg': 'div(add(mul(^0, ^2), mul(^1, sub(1, ^2))), 1)',
        
        # 更复杂的组合
        'complex_factor': 'add(zscore(^0), normalize(^1))',
        'final_score': 'weighted_avg(complex_factor(^0, ^1), mean(^0), 0.7)',
    }
    
    print(f"Refbook 规模: {len(refbook)} 条规则")
    
    # 测试初始化性能
    start_time = time.time()
    compressor = TreeCompressor(refbook)
    init_time = time.time() - start_time
    
    print(f"初始化时间: {init_time:.4f}s")
    
    # 检查最复杂的展开
    max_length = 0
    max_name = ""
    for name, expr in compressor.expanded_refbook.items():
        if len(expr) > max_length:
            max_length = len(expr)
            max_name = name
    
    print(f"最复杂展开: {max_name} ({max_length} 字符)")
    print(f"  {compressor.expanded_refbook[max_name]}")
    
    # 测试压缩性能
    test_expr = "final_score(data1, data2)"
    
    start_time = time.time()
    for _ in range(100):
        compressed = compressor.compress(test_expr)
    compress_time = time.time() - start_time
    
    print(f"\n压缩性能 (100次): {compress_time:.4f}s")
    print(f"平均单次: {compress_time/100*1000:.2f}ms")
    print(f"压缩结果: {compressed}")

def test_edge_cases():
    """测试边界情况"""
    
    print("\n" + "=" * 60)
    print("边界情况测试")
    print("=" * 60)
    
    # 测试空refbook
    print("1. 空 refbook:")
    empty_compressor = TreeCompressor({})
    test_expr = "add(x, y)"
    result = empty_compressor.compress(test_expr)
    print(f"  输入: {test_expr}")
    print(f"  结果: {result}")
    print()
    
    # 测试自引用
    print("2. 自引用:")
    self_ref_book = {'A': 'A(^0)'}
    try:
        self_compressor = TreeCompressor(self_ref_book)
        print(f"  展开结果: {self_compressor.expanded_refbook}")
    except Exception as e:
        print(f"  处理自引用出错: {e}")
    print()
    
    # 测试复杂参数
    print("3. 复杂参数:")
    complex_book = {
        'F': 'add(^0, ^1)',
        'G': 'F(mul(^0, 2), sub(^1, 1))'
    }
    complex_compressor = TreeCompressor(complex_book)
    print(f"  G 展开: {complex_compressor.expanded_refbook['G']}")
    
    test_expr = "G(x, y)"
    expanded = complex_compressor._expand_expression(test_expr, complex_compressor.expanded_refbook)
    print(f"  G(x, y) 展开: {expanded}")

if __name__ == "__main__":
    test_basic_expansion()
    test_no_dependencies()
    test_complex_dependencies()
    test_performance()
    test_edge_cases()
    
    print("\n" + "=" * 60)
    print("迭代展开测试完成！")
    print("=" * 60)
    print("\n验证内容:")
    print("✅ 基础的refbook相互引用展开")
    print("✅ 无依赖情况的正确处理")
    print("✅ 复杂多层依赖的完全展开")
    print("✅ 展开功能的性能表现")
    print("✅ 各种边界情况的处理")
