"""
测试大小写敏感性 - 确保没有任何大小写转换
"""

from tree_compressor import TreeCompressor

def test_case_sensitivity():
    """测试大小写敏感性"""
    
    print("=" * 60)
    print("大小写敏感性测试")
    print("=" * 60)
    
    # 测试大小写敏感的refbook
    refbook = {
        'Add': 'add(^0, ^1)',        # 大写A
        'ADD': 'Add(^0, ^1)',        # 全大写，依赖大写A
        'add_func': 'Add(^0, 1)',    # 小写，依赖大写A
        'MUL': 'mul(^0, ^1)',        # 大写
        'mul': 'MUL(^0, ^1)',        # 小写，依赖大写
    }
    
    print("大小写敏感的 refbook:")
    for name, expr in refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    compressor = TreeCompressor(refbook)
    
    print("展开后的 refbook:")
    for name, expr in compressor.expanded_refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    # 测试不同大小写的表达式
    test_cases = [
        "Add(x, y)",           # 匹配 Add
        "ADD(x, y)",           # 匹配 ADD
        "add_func(x)",         # 匹配 add_func
        "MUL(a, b)",           # 匹配 MUL
        "mul(a, b)",           # 匹配 mul
        "add(x, y)",           # 不匹配任何（原始add函数）
    ]
    
    for test_expr in test_cases:
        print(f"测试表达式: {test_expr}")
        
        # 展开
        expanded = compressor._expand_expression(test_expr, compressor.expanded_refbook)
        print(f"  展开结果: {expanded}")
        
        # 压缩
        compressed = compressor.compress(test_expr)
        print(f"  压缩结果: {compressed}")
        
        # 验证大小写保持
        if test_expr == compressed:
            print(f"  ✓ 保持原样（未匹配到refbook）")
        else:
            print(f"  ✓ 成功压缩")
        print()

def test_mixed_case_expressions():
    """测试混合大小写表达式"""
    
    print("=" * 60)
    print("混合大小写表达式测试")
    print("=" * 60)
    
    refbook = {
        'CamelCase': 'func(^0)',
        'snake_case': 'other(^0)',
        'UPPER_CASE': 'another(^0)',
        'mixedCASE': 'final(^0)',
    }
    
    print("混合大小写 refbook:")
    for name, expr in refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    compressor = TreeCompressor(refbook)
    
    test_expressions = [
        "CamelCase(data)",
        "snake_case(data)", 
        "UPPER_CASE(data)",
        "mixedCASE(data)",
        "camelcase(data)",      # 不同大小写，应该不匹配
        "SNAKE_CASE(data)",     # 不同大小写，应该不匹配
    ]
    
    for expr in test_expressions:
        print(f"表达式: {expr}")
        compressed = compressor.compress(expr)
        print(f"  压缩结果: {compressed}")
        
        if expr == compressed:
            print(f"  → 未匹配（大小写不同）")
        else:
            print(f"  → 成功匹配并压缩")
        print()

def test_parameter_case_sensitivity():
    """测试参数的大小写敏感性"""
    
    print("=" * 60)
    print("参数大小写敏感性测试")
    print("=" * 60)
    
    refbook = {
        'F': 'func(^0, ^1)',
        'G': 'F(Data, ^0)',      # 注意这里用的是 Data（大写D）
    }
    
    print("参数大小写测试 refbook:")
    for name, expr in refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    compressor = TreeCompressor(refbook)
    
    print("展开后:")
    for name, expr in compressor.expanded_refbook.items():
        print(f"  {name}: {expr}")
    print()
    
    test_cases = [
        "G(value)",           # 应该展开为 func(Data, value)
        "F(Data, value)",     # 应该展开为 func(Data, value)
        "F(data, value)",     # 应该展开为 func(data, value) - 注意小写d
    ]
    
    for expr in test_cases:
        print(f"表达式: {expr}")
        expanded = compressor._expand_expression(expr, compressor.expanded_refbook)
        print(f"  展开: {expanded}")
        compressed = compressor.compress(expr)
        print(f"  压缩: {compressed}")
        print()

def test_output_format():
    """测试输出格式保持原始大小写"""
    
    print("=" * 60)
    print("输出格式测试")
    print("=" * 60)
    
    refbook = {
        'MyFunc': 'add(^0, ^1)',
        'AnotherFunc': 'MyFunc(^0, 1)',
    }
    
    compressor = TreeCompressor(refbook)
    
    test_expr = "AnotherFunc(someVariable)"
    print(f"输入表达式: {test_expr}")
    
    # 展开
    expanded = compressor._expand_expression(test_expr, compressor.expanded_refbook)
    print(f"完全展开: {expanded}")
    
    # 压缩
    compressed = compressor.compress(test_expr)
    print(f"压缩结果: {compressed}")
    
    # 验证变量名大小写保持
    print(f"\n大小写保持验证:")
    print(f"  原始变量: someVariable")
    print(f"  结果中的变量: {compressed.split('(')[1].split(')')[0] if '(' in compressed else 'N/A'}")
    
    # 验证函数名大小写保持
    if 'MyFunc' in compressed:
        print(f"  ✓ 函数名 MyFunc 大小写正确保持")
    else:
        print(f"  ✗ 函数名大小写可能有问题")

if __name__ == "__main__":
    test_case_sensitivity()
    test_mixed_case_expressions()
    test_parameter_case_sensitivity()
    test_output_format()
    
    print("=" * 60)
    print("大小写敏感性测试完成！")
    print("=" * 60)
    print("\n验证内容:")
    print("✅ refbook 键名大小写敏感")
    print("✅ 表达式匹配大小写敏感")
    print("✅ 参数名大小写保持")
    print("✅ 输出格式大小写保持")
    print("✅ 变量名大小写保持")
