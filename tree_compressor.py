"""
简化版语法树压缩算法 - 减少不必要的辅助函数

主要改进：
1. 将只调用一次的辅助函数融入主函数
2. 简化代码结构，提高可读性
3. 保持功能完整性
"""

from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
from copy import deepcopy
from itertools import product

@dataclass
class TreeNode:
    """算子树节点"""
    operator: str
    children: List['TreeNode']
    is_leaf: bool = False
    depth: int = 0
    height: int = 0

    def __str__(self):
        if self.is_leaf or not self.children:
            return f"{self.operator}()" if not self.is_leaf and not self.children else self.operator
        children_str = ','.join(str(child) for child in self.children)
        return f"{self.operator}({children_str})"

    def get_info(self):
        return f"{self.operator} (depth={self.depth}, height={self.height})"


class ExpressionParser:
    """表达式解析器"""
    
    @staticmethod
    def split_bracket(expr: str) -> <PERSON><PERSON>[str, List[str]]:
        """分割函数名和参数"""
        if '(' not in expr:
            return expr, []
        
        parts = expr.split('(', 1)
        func_name = parts[0]
        args_str = parts[1].rstrip(')')
        
        if not args_str.strip():
            return func_name, []
        
        return func_name, ExpressionParser.split_comma(args_str)
    
    @staticmethod
    def split_comma(expr: str) -> List[str]:
        """按逗号分割参数，处理嵌套括号"""
        if not expr.strip():
            return []
        
        result = []
        bracket_count = 0
        current_arg = ''
        
        for char in expr:
            if char == ',' and bracket_count == 0:
                result.append(current_arg.strip())
                current_arg = ''
            else:
                current_arg += char
                if char == '(':
                    bracket_count += 1
                elif char == ')':
                    bracket_count -= 1
        
        if current_arg.strip():
            result.append(current_arg.strip())
        
        return result
    
    @staticmethod
    def parse_to_tree(expr: str, depth: int = 0) -> TreeNode:
        """将表达式解析为语法树"""
        expr = expr.strip()

        if '(' not in expr:
            return TreeNode(operator=expr, children=[], is_leaf=True, depth=depth, height=0)

        func_name, args = ExpressionParser.split_bracket(expr)

        children = []
        max_child_height = -1

        for arg in args:
            child_node = ExpressionParser.parse_to_tree(arg, depth + 1)
            children.append(child_node)
            max_child_height = max(max_child_height, child_node.height)

        current_height = max_child_height + 1 if children else 0

        return TreeNode(
            operator=func_name,
            children=children,
            is_leaf=False,
            depth=depth,
            height=current_height
        )


class TreeCompressor:
    """语法树压缩器"""
    
    def __init__(self, refbook: Dict[str, str]):
        self.refbook = refbook
        # 首先完全展开refbook中的所有表达式，消除相互引用
        self.expanded_refbook = self._fully_expand_refbook(refbook)
        # 然后构建展开后的refbook树
        self.refbook_trees = {}
        for name, pattern in self.expanded_refbook.items():
            try:
                self.refbook_trees[name] = ExpressionParser.parse_to_tree(pattern, depth=0)
            except Exception as e:
                print(f"Warning: Failed to parse expanded refbook pattern '{name}': {pattern}. Error: {e}")

    def _fully_expand_refbook(self, refbook: Dict[str, str]) -> Dict[str, str]:
        """完全展开refbook，消除表达式间的相互引用"""
        expanded = {}
        # 保持原始大小写
        normalized_refbook = refbook.copy()

        # 使用拓扑排序确定展开顺序
        dependency_graph = {}
        for name, expr in normalized_refbook.items():
            dependencies = self._find_dependencies(expr, normalized_refbook.keys())
            dependency_graph[name] = dependencies

        # 拓扑排序
        sorted_names = self._topological_sort(dependency_graph)

        # 按拓扑顺序逐个展开
        for name in sorted_names:
            expr = normalized_refbook[name]
            expanded_expr = self._expand_expression(expr, expanded)
            expanded[name] = expanded_expr

        return expanded

    def _find_dependencies(self, expr: str, refbook_names: set) -> set:
        """找到表达式中依赖的refbook名称"""
        dependencies = set()

        # 使用栈来遍历表达式树，查找依赖
        if '(' not in expr:
            # 叶子节点
            if expr in refbook_names:
                dependencies.add(expr)
            return dependencies

        try:
            func_name, args = ExpressionParser.split_bracket(expr)
            if func_name in refbook_names:
                dependencies.add(func_name)

            for arg in args:
                dependencies.update(self._find_dependencies(arg, refbook_names))
        except:
            pass

        return dependencies

    def _topological_sort(self, dependency_graph: Dict[str, set]) -> List[str]:
        """拓扑排序，确定展开顺序"""
        result = []
        visited = set()
        temp_visited = set()

        def dfs(node):
            if node in temp_visited:
                # 检测到循环依赖，跳过
                return
            if node in visited:
                return

            temp_visited.add(node)
            for dependency in dependency_graph.get(node, set()):
                if dependency in dependency_graph:  # 只处理存在的依赖
                    dfs(dependency)
            temp_visited.remove(node)
            visited.add(node)
            result.append(node)

        for node in dependency_graph:
            if node not in visited:
                dfs(node)

        return result

    def _expand_expression(self, expr: str, expanded_refbook: Dict[str, str]) -> str:
        """展开单个表达式，替换其中的refbook引用"""
        if '(' not in expr:
            # 叶子节点
            if expr in expanded_refbook:
                return expanded_refbook[expr]
            return expr

        try:
            func_name, args = ExpressionParser.split_bracket(expr)

            # 递归展开参数
            expanded_args = []
            for arg in args:
                expanded_arg = self._expand_expression(arg, expanded_refbook)
                expanded_args.append(expanded_arg)

            # 检查函数名是否需要展开
            if func_name in expanded_refbook:
                # 需要展开函数定义
                func_definition = expanded_refbook[func_name]
                # 替换参数占位符
                expanded_func = func_definition
                for i, arg in enumerate(expanded_args):
                    expanded_func = expanded_func.replace(f'^{i}', arg)
                return self._expand_expression(expanded_func, expanded_refbook)
            else:
                # 不需要展开，重新组装
                if expanded_args:
                    return f"{func_name}({','.join(expanded_args)})"
                else:
                    return f"{func_name}()"
        except:
            return expr

    def compress(self, expr: str) -> str:
        """
        压缩表达式：贪心算法，子树先规约
        """
        # 首先展开输入表达式中的refbook引用
        expanded_expr = self._expand_expression(expr, self.expanded_refbook)
        tree = ExpressionParser.parse_to_tree(expanded_expr, depth=0)

        # 使用栈来递归约束
        stack = [tree]
        processed = {}

        while stack:
            current = stack[-1]

            if current.is_leaf:
                processed[id(current)] = current
                stack.pop()
                continue

            # 检查所有子节点是否已处理
            all_children_processed = True
            for child in current.children:
                if id(child) not in processed:
                    stack.append(child)
                    all_children_processed = False

            if not all_children_processed:
                continue

            # 所有子节点已处理，处理当前节点
            stack.pop()

            # 获取压缩后的子节点
            compressed_children = [processed[id(child)] for child in current.children]

            # 创建当前节点
            height = 0 if not compressed_children else max(child.height for child in compressed_children) + 1
            current_node = TreeNode(
                operator=current.operator,
                children=compressed_children,
                is_leaf=current.is_leaf,
                depth=current.depth,
                height=height
            )

            # 尝试匹配refbook中的模式
            matched = False
            for ref_name, ref_tree in self.refbook_trees.items():
                match_result = self._try_match_pattern(current_node, ref_tree)
                if match_result is not None:
                    remaining_args, param_map = match_result

                    # 构建参数列表
                    new_args = list(remaining_args)
                    for index in sorted([int(name[1:]) for name in param_map.keys() if name.startswith('^') and name[1:].isdigit()]):
                        param_name = f'^{index}'
                        if param_name in param_map:
                            new_args.append(param_map[param_name])

                    # 创建规约后的节点
                    new_height = 0 if not new_args else max(arg.height for arg in new_args) + 1
                    processed[id(current)] = TreeNode(
                        operator=ref_name,
                        children=new_args,
                        is_leaf=False,
                        depth=current.depth,
                        height=new_height
                    )
                    matched = True
                    break

            if not matched:
                processed[id(current)] = current_node

        return str(processed[id(tree)])

    def _try_match_pattern(self, node: TreeNode, pattern: TreeNode) -> Optional[Tuple[List[TreeNode], Dict[str, TreeNode]]]:
        """尝试匹配模式，返回(剩余参数, 参数映射)或None"""
        param_map = {}

        # 使用栈模拟匹配
        stack = [(node, pattern, 'match')]

        while stack:
            current_node, current_pattern, action = stack.pop()

            if action == 'match':
                # 参数占位符处理
                if current_pattern.is_leaf and current_pattern.operator.startswith('^'):
                    param_name = current_pattern.operator
                    if param_name in param_map:
                        # 比较两个树是否相等
                        compare_stack = [(current_node, param_map[param_name])]
                        trees_equal = True

                        while compare_stack and trees_equal:
                            t1, t2 = compare_stack.pop()
                            if (t1.is_leaf != t2.is_leaf or t1.operator != t2.operator or len(t1.children) != len(t2.children)):
                                trees_equal = False
                                break
                            for c1, c2 in zip(t1.children, t2.children):
                                compare_stack.append((c1, c2))

                        if not trees_equal:
                            return None
                    else:
                        param_map[param_name] = deepcopy(current_node)
                    continue

                # 叶子节点匹配
                if current_pattern.is_leaf:
                    if not (current_node.is_leaf and current_node.operator == current_pattern.operator):
                        return None
                    continue

                # 节点类型不匹配
                if current_node.is_leaf or current_node.operator != current_pattern.operator:
                    return None

                # 参数数量检查
                if len(current_node.children) < len(current_pattern.children):
                    return None

                # 添加子节点匹配任务
                for i, pattern_child in enumerate(current_pattern.children):
                    if i >= len(current_node.children):
                        return None
                    stack.append((current_node.children[i], pattern_child, 'match'))

        # 返回剩余参数
        remaining_args = node.children[len(pattern.children):] if not pattern.is_leaf else []
        return (remaining_args, param_map)

    def get_tree_info(self, expr: str) -> str:
        """获取表达式树的详细信息"""
        tree = ExpressionParser.parse_to_tree(expr, depth=0)

        result = []
        stack = [(tree, "")]

        while stack:
            node, indent = stack.pop()
            result.append(f"{indent}{node.get_info()}")

            for child in reversed(node.children):
                stack.append((child, indent + "  "))

        return "\n".join(result) + "\n"

    def find_all_reductions(self, expr: str, max_depth: int = 10) -> List[str]:
        """
        查找所有可能的最终规约表达式：
            bfs枚举，逐个弹出先入先出的元素后剩下的字符串由左到右进行规约尝试（即优先规约根节点一侧），后从叶子结点回溯再看从叶节点一侧的优先规约，二者的不冲突前提下的笛卡尔积就是全部可能规约种类
        """
        # 首先展开输入表达式中的refbook引用
        expanded_expr = self._expand_expression(expr, self.expanded_refbook)
        tree = ExpressionParser.parse_to_tree(expanded_expr, depth=0)
        all_forms = set()

        work_queue = [(tree, 0)]
        processed_nodes = set()

        while work_queue:
            current_node, depth = work_queue.pop(0)

            if depth >= max_depth:
                continue

            node_key = (str(current_node), depth)
            if node_key in processed_nodes:
                continue
            processed_nodes.add(node_key)

            all_forms.add(str(current_node))

            if current_node.is_leaf:
                continue

            # 为每个子节点收集所有形式
            child_form_lists = []
            for child in current_node.children:
                child_forms = set([str(child)])
                work_queue.append((child, depth + 1))

                # 收集已知的子节点形式
                for form in all_forms:
                    if form.startswith(child.operator):
                        child_forms.add(form)
                child_form_lists.append(list(child_forms))

            # 生成所有子节点组合
            for combination in product(*child_form_lists):
                try:
                    # 解析组合中的每个子节点
                    child_nodes = []
                    for form_str in combination:
                        child_tree = ExpressionParser.parse_to_tree(form_str, depth=current_node.depth + 1)
                        child_nodes.append(child_tree)

                    # 创建新节点
                    height = 0 if not child_nodes else max(c.height for c in child_nodes) + 1
                    new_node = TreeNode(
                        operator=current_node.operator,
                        children=child_nodes,
                        is_leaf=False,
                        depth=current_node.depth,
                        height=height
                    )

                    new_node_str = str(new_node)
                    if new_node_str not in all_forms:
                        all_forms.add(new_node_str)
                        work_queue.append((new_node, depth))

                    # 尝试所有refbook规约
                    for ref_name, ref_tree in self.refbook_trees.items():
                        match_result = self._try_match_pattern(new_node, ref_tree)
                        if match_result is not None:
                            remaining_args, param_map = match_result

                            # 构建参数列表
                            new_args = list(remaining_args)
                            for index in sorted([int(name[1:]) for name in param_map.keys() if name.startswith('^') and name[1:].isdigit()]):
                                param_name = f'^{index}'
                                if param_name in param_map:
                                    new_args.append(param_map[param_name])

                            # 创建规约节点
                            reduced_height = 0 if not new_args else max(arg.height for arg in new_args) + 1
                            reduced_node = TreeNode(
                                operator=ref_name,
                                children=new_args,
                                is_leaf=False,
                                depth=current_node.depth,
                                height=reduced_height
                            )

                            reduced_str = str(reduced_node)
                            if reduced_str not in all_forms:
                                all_forms.add(reduced_str)
                                if depth < max_depth - 1:
                                    work_queue.append((reduced_node, depth + 1))

                except:
                    continue

        original_expr = str(tree)
        all_forms.discard(original_expr)

        return sorted([form for form in all_forms if self.compress(form) == form])


# test
def compress_expression(expr: str, refbook: Dict[str, str]) -> str:
    # 贪心法，先压子树
    return TreeCompressor(refbook).compress(expr)

def find_all_expression_reductions(expr: str, refbook: Dict[str, str], max_depth: int = 10) -> List[str]:
    # 穷举
    return TreeCompressor(refbook).find_all_reductions(expr, max_depth)
