# -*- coding: utf-8 -*-

# Copyright (c) JoinQuant Development Team
# Author: <PERSON><PERSON><PERSON> <<EMAIL>>

import os
import time
import datetime
import logging
from collections import OrderedDict
from functools import lru_cache, wraps
from importlib import import_module

import SharedArray
import numpy as np
import pandas as pd


logger = logging.getLogger('level2_utils')


def do_nothing_decorator(*decorator_args, **decorator_kwargs):
    if len(decorator_args) == 1 and callable(decorator_args[0]):
        func = decorator_args[0]

        @wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        return wrapper
    else:
        def decorator(func):

            @wraps(func)
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)

            return wrapper

        return decorator


try:
    import cython
    CYTHON_COMPILED = cython.compiled
except (ImportError, AttributeError):
    CYTHON_COMPILED = False

try:
    import ft_common_base
except ImportError as ex:
    logger.warning("import ft_common_base failed: %s", ex)
    attach_sa = SharedArray.attach
    read_npz = np.load
    try:
        read_feather = import_module('feather').read_dataframe
    except ImportError:
        pass
    cache_with_ignore = do_nothing_decorator
    get_per_factor_process_max_tasks = lambda default=None: default
else:
    attach_sa = ft_common_base.safe_attach_sa
    read_npz = ft_common_base.read_npz
    read_feather = ft_common_base.read_feather
    cache_with_ignore = ft_common_base.cache_with_ignore
    dt2ts = ft_common_base.dt2ts
    get_per_factor_process_max_tasks = ft_common_base.get_per_factor_process_max_tasks

try:
    if not CYTHON_COMPILED:
        from numba import njit
    else:
        njit = do_nothing_decorator
except ImportError as ex:
    logger.warning("import numba.njit failed: %s", ex)
    njit = do_nothing_decorator


get_today = datetime.date.today
get_now = datetime.datetime.now


# 期货交易所
FUTURE_EXCHANGES = {'CCFX', 'XSGE', 'XDCE', 'XZCE', 'XINE', 'GFEX'}


STOCK_EXCHANGES = {'XSHG', 'XSHE'}


MAX_INT32 = 2147483647

PRICE_MULTIPLIER = 10000

ONE_MILLION = 1000000


def is_live_trade():
    return os.getenv("JQ_LIVE_TRADE") == "1"


class PathMixin(object):

    # 网盘 sanp 位置
    _snap_dir = None
    # 网盘 sanp level1 位置
    _snap_level1_dir = None
    # 网盘 sanp level2 位置
    _snap_level2_dir = None

    # 网盘 fastjqdata 位置
    _fastjqdata_dir = None

    # 网盘 livetrade 位置
    _livetrade_nas_dir = "/livetrade"

    # npz数据位置
    _npz_dir = None
    _shm_dir = None

    @classmethod
    def get_snap_dir(cls):
        if not cls._snap_dir:
            for path in [
                "/snap",
                "/opt/data/extra/snap",
                "/tick/level1/market-snap",
                os.path.join(os.getenv("HOME"), "snap"),
            ]:
                if os.path.isdir(path):
                    break
            cls._snap_dir = path
        return cls._snap_dir

    @classmethod
    def get_snap_level1_dir(cls):
        if not cls._snap_level1_dir:
            for path in [
                "/opt/data/extra/level1",
                os.path.join(cls.get_snap_dir(), "level1"),
            ]:
                if os.path.isdir(path):
                    break
            cls._snap_level1_dir = path
        return cls._snap_level1_dir

    @classmethod
    def get_snap_level2_dir(cls):
        if not cls._snap_level2_dir:
            for path in [
                "/opt/data/extra/level2",
                os.path.join(cls.get_snap_dir(), "level2"),
            ]:
                if os.path.isdir(path):
                    break
            cls._snap_level2_dir = path
        return cls._snap_level2_dir

    @classmethod
    def get_fastjqdata_dir(cls):
        if not cls._fastjqdata_dir:
            if is_live_trade():
                cls._fastjqdata_dir = cls._livetrade_nas_dir
                return cls._fastjqdata_dir

            for path in [
                "/opt/data/jq/bundle/fastjqdata",
                "/fastjqdata",
                os.path.join(os.getenv("HOME"), "fastjqdata"),
            ]:
                if os.path.isdir(path):
                    break
            cls._fastjqdata_dir = path

        return cls._fastjqdata_dir

    @classmethod
    def get_shm_dir(cls):
        if not cls._shm_dir:
            for _path in [
                "/dev/shm",
            ]:
                if os.path.isdir(_path):
                    break
            cls._shm_dir = _path
        return cls._shm_dir

    @classmethod
    def get_npz_dir(cls):
        if not cls._npz_dir:
            for _path in [
                "/opt/data/extra/data",
                "/opt/data/jq/bundle/fastjqdata",
            ]:
                if os.path.isdir(_path):
                    break
            cls._npz_dir = _path
        return cls._npz_dir


class Level2Tick(PathMixin):

    # 期货 level2 基础 tick 数据字段
    FUTURE_L2TICK_COLUMNS = [
        'time', 'current', 'high', 'low', 'volume', 'money', 'position',
        'a1_v', 'a2_v', 'a3_v', 'a4_v', 'a5_v',
        'a1_p', 'a2_p', 'a3_p', 'a4_p', 'a5_p',
        'b1_v', 'b2_v', 'b3_v', 'b4_v', 'b5_v',
        'b1_p', 'b2_p', 'b3_p', 'b4_p', 'b5_p',
        'open', 'high_limit', 'low_limit'
    ]

    # 股票 level2 tick 数据字段
    STOCK_L2TICK_COLUMNS = [
        'time', 'current', 'high', 'low', 'volume', 'money',
        'a1_v', 'a2_v', 'a3_v', 'a4_v', 'a5_v', 'a6_v', 'a7_v', 'a8_v', 'a9_v', 'a10_v',
        'a1_p', 'a2_p', 'a3_p', 'a4_p', 'a5_p', 'a6_p', 'a7_p', 'a8_p', 'a9_p', 'a10_p',
        'b1_v', 'b2_v', 'b3_v', 'b4_v', 'b5_v', 'b6_v', 'b7_v', 'b8_v', 'b9_v', 'b10_v',
        'b1_p', 'b2_p', 'b3_p', 'b4_p', 'b5_p', 'b6_p', 'b7_p', 'b8_p', 'b9_p', 'b10_p',
        'open', 'high_limit', 'low_limit'
    ]

    STOCK_L2TICK_NEW_COLUMNS = [
        'time', 'current', 'high', 'low', 'volume', 'money',
        'a1_v', 'a2_v', 'a3_v', 'a4_v', 'a5_v', 'a6_v', 'a7_v', 'a8_v', 'a9_v', 'a10_v',
        'a1_p', 'a2_p', 'a3_p', 'a4_p', 'a5_p', 'a6_p', 'a7_p', 'a8_p', 'a9_p', 'a10_p',
        'b1_v', 'b2_v', 'b3_v', 'b4_v', 'b5_v', 'b6_v', 'b7_v', 'b8_v', 'b9_v', 'b10_v',
        'b1_p', 'b2_p', 'b3_p', 'b4_p', 'b5_p', 'b6_p', 'b7_p', 'b8_p', 'b9_p', 'b10_p',
        'open', 'high_limit', 'low_limit',
        'iopv', 'trade_num',
        'total_ask_qty', 'avg_ask_price', 'total_bid_qty', 'avg_bid_price',
        'a1_n', 'a2_n', 'a3_n', 'a4_n', 'a5_n', 'a6_n', 'a7_n', 'a8_n', 'a9_n', 'a10_n',
        'b1_n', 'b2_n', 'b3_n', 'b4_n', 'b5_n', 'b6_n', 'b7_n', 'b8_n', 'b9_n', 'b10_n',
    ]

    # 大商所期货 level2 tick 数据字段
    XDCE_L2TICK_COLUMNS = FUTURE_L2TICK_COLUMNS + [
        'd_a1_v', 'd_a2_v', 'd_a3_v', 'd_a4_v', 'd_a5_v',
        'd_b1_v', 'd_b2_v', 'd_b3_v', 'd_b4_v', 'd_b5_v',
    ]

    @classmethod
    def get_sa_l2tick_path(cls, code, date, exchange=None):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        if not exchange:
            exchange = code.rsplit('.', 1)[-1]
        return os.path.join(
            cls.get_fastjqdata_dir(), "l2ticks-sa",
            date.strftime("%Y%m/%Y-%m-%d"),
            exchange, code,
        )

    @classmethod
    @lru_cache(maxsize=get_per_factor_process_max_tasks(default=0))
    def load_l2ticks_from_sa(cls, code=None, date=None, path=None):
        """从 SharedArray 格式存储中加载一天的 level2 tick 数据"""
        if path:
            if not code:
                code = os.path.basename(path)
            exchange = code.rsplit('.', 1)[-1]
        else:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            exchange = code.rsplit('.', 1)[-1]
            path = cls.get_sa_l2tick_path(code, date, exchange=exchange)

        if exchange == "XDCE":
            columns = cls.XDCE_L2TICK_COLUMNS
        else:
            columns = cls.FUTURE_L2TICK_COLUMNS

        if not os.path.exists(path):
            return pd.DataFrame(columns=columns)

        arr = attach_sa('file://' + path, readonly=True)
        return pd.DataFrame(arr, columns=columns)

    @classmethod
    def get_shm_l2tick_path(cls, code, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()

        custom_shm_root = get_custom_shm_root()
        if not custom_shm_root:
            custom_shm_root = cls.get_shm_dir()

        return os.path.join(
            custom_shm_root,
            "l2tick",
            date.strftime("%Y-%m-%d"),
            code,
        )

    @classmethod
    def get_npz_l2tick_path(cls, code, date, exchange=None):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        if not exchange:
            exchange = code.rsplit('.', 1)[-1]
        return os.path.join(
            cls.get_fastjqdata_dir(), "l2ticks-npz",
            date.strftime("%Y%m/%Y-%m-%d"),
            exchange, code + ".npz",
        )

    @classmethod
    def get_feather_l2tick_path(cls, code, date, exchange=None):
        from jqdata.apis import get_security_info
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        if not exchange:
            exchange = code.rsplit('.', 1)[-1]
        code_type = get_security_info(code)._type.value
        if not code_type.endswith("s"):
            code_type = code_type + 's'
        return os.path.join(
            cls.get_fastjqdata_dir(),
            "l2ticks-feather",
            code_type,
            date.strftime("%Y%m/%Y-%m-%d"),
            exchange, code + ".feather",
        )

    @classmethod
    def load_l2ticks_from_shm(cls, code=None, date=None, path=None):
        """从共享内存中加载一天的 level2 tick 数据"""
        if path:
            if not code:
                code = os.path.basename(path)
            exchange = code.rsplit('.', 1)[-1]
        else:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            exchange = code.rsplit('.', 1)[-1]
            path = cls.get_shm_l2tick_path(code, date)

        if exchange == "XDCE":
            columns = cls.XDCE_L2TICK_COLUMNS
        elif exchange in STOCK_EXCHANGES:
            columns = cls.STOCK_L2TICK_COLUMNS
            if code.startswith('1') or code.startswith('5'):
                columns = cls.STOCK_L2TICK_NEW_COLUMNS
        else:
            columns = cls.FUTURE_L2TICK_COLUMNS

        if not os.path.exists(path):
            return pd.DataFrame(columns=columns)

        arr = attach_sa('file://' + path, readonly=True)
        ixgrid = np.ix_(arr[:, 0] < 2147483647.0, range(len(columns)))  # 丢弃最后一列的update_time
        arr = arr[ixgrid]
        return pd.DataFrame(data=arr, columns=columns)

    @classmethod
    @lru_cache(maxsize=get_per_factor_process_max_tasks(default=0))
    def load_l2ticks_from_npz(cls, code=None, date=None, path=None):
        """从 NPZ 格式存储中加载一天的 level2 tick 数据"""
        if path:
            if not code:
                code = os.path.basename(path).replace('.npz', '')
            exchange = code.rsplit('.', 1)[-1]
        else:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            exchange = code.rsplit('.', 1)[-1]
            path = cls.get_npz_l2tick_path(code, date, exchange=exchange)

        if exchange == "XDCE":
            columns = cls.XDCE_L2TICK_COLUMNS
        elif exchange in STOCK_EXCHANGES:
            columns = cls.STOCK_L2TICK_COLUMNS
            if code.startswith('1') or code.startswith('5'):
                columns = cls.STOCK_L2TICK_NEW_COLUMNS
        else:
            columns = cls.FUTURE_L2TICK_COLUMNS

        if not os.path.exists(path):
            return pd.DataFrame(columns=columns)

        loaded = read_npz(path, allow_pickle=True)
        try:
            columns = loaded["columns"]
        except KeyError:
            pass
        return pd.DataFrame(data=loaded["data"], columns=columns)

    @classmethod
    @lru_cache(maxsize=get_per_factor_process_max_tasks(default=0))
    def load_l2ticks_from_feather(cls, code=None, date=None, path=None, fields=None):
        """从 FEATHER 格式存储中加载一天的 level2 tick 数据"""
        if path:
            if not code:
                code = os.path.basename(path).replace('.feather', '')
            exchange = code.rsplit('.', 1)[-1]
        else:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            exchange = code.rsplit('.', 1)[-1]
            path = cls.get_feather_l2tick_path(code, date, exchange=exchange)

        if exchange == "XDCE":
            all_columns = cls.XDCE_L2TICK_COLUMNS
        elif exchange in STOCK_EXCHANGES:
            all_columns = cls.STOCK_L2TICK_NEW_COLUMNS
        else:
            all_columns = cls.FUTURE_L2TICK_COLUMNS

        if not os.path.exists(path):
            if fields is None:
                return pd.DataFrame(columns=all_columns)
            return pd.DataFrame(columns=fields)

        if fields is None:
            return read_feather(path).astype("float64")
        return read_feather(path, columns=fields).astype("float64")


def load_l2ticks(code=None, date=None, path=None, fields=None):
    """加载 Level2 Tick 数据

    参数：
        code: 标的，需带交易所后缀
        date: 加载数据的日期
        path: 从指定文件中加载数据，如果文件名不是标的名时，还需要指定 code 参数
              与 code 和 date 参数二选一，仅指定 code 和 date 时会自动搜索数据文件

    在date为当日/下一个交易日时获取共享内存数据，历史数据从csv中获取
    返回 pd.DataFrame，列为数据字段名，所有标的均有如下通用字段：
        time,current,high,low,volume,money,position,
        a1_v,a2_v,a3_v,a4_v,a5_v,a1_p,a2_p,a3_p,a4_p,a5_p,
        b1_v,b2_v,b3_v,b4_v,b5_v,b1_p,b2_p,b3_p,b4_p,b5_p,
        open,high_limit,low_limit
    由于各交易所可能有自己额外的数据字段，所以不同交易所会在以上基础数据字段后追加额外的字段

    部分交易的数据时间带不规则的毫秒数，由于把数据都存成了 float 类型，
    所以精度可能会丢失，建议使用如下的方式转化时间：
        import ft_common_base
        df['time'] = ft_common_base.ts2dt(df.time)

    大商所额外数据字段：
        d_a1_v,d_a2_v,d_a3_v,d_a4_v,d_a5_v  # 申卖推导量
        d_b1_v,d_b2_v,d_b3_v,d_b4_v,d_b5_v  # 申买推导量
    """
    use_shm_flag = 1
    use_feather_flag = 1

    if path:
        _, ext_name = os.path.splitext(path)
        if ext_name in ('.csv', '.npz', '.feather'):
            use_shm_flag = 0
        if not code:
            code = os.path.basename(path)
            if ext_name and ext_name not in FUTURE_EXCHANGES | STOCK_EXCHANGES:
                code = code.replace(ext_name, '')
    else:
        assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
        shm_path = Level2Tick.get_shm_l2tick_path(code, date)
        if not os.path.exists(shm_path):
            use_shm_flag = 0

    if force_use_shm():
        use_shm_flag = 1
    if force_no_use_shm():
        use_shm_flag = 0

    exchange = code.rsplit('.', 1)[-1]
    if fields is None and exchange in STOCK_EXCHANGES:
        if force_use_feather():
            fields = Level2Tick.STOCK_L2TICK_NEW_COLUMNS
        else:
            fields = Level2Tick.STOCK_L2TICK_COLUMNS

    if use_shm_flag:
        df = Level2Tick.load_l2ticks_from_shm(code=code, date=date, path=path)
    else:
        df = Level2Tick.load_l2ticks_from_feather(
            code=code, date=date, path=path,
            fields=tuple(fields) if fields else None
        )

    # 股票l2tick-npz、shm 没有positions字段，使用NaN填充一下
    if code[-4:] in STOCK_EXCHANGES:
        df["position"] = pd.Series(dtype="float64")

    if fields is not None:
        df = df[fields]

    return df


class L2TickDataChecker(object):

    def __init__(self, check_time, stocks=None, date=None, shm_rootdir=None):
        self.logger = logger.getChild(self.__class__.__name__)

        self.check_time = check_time
        self.date = (pd.to_datetime(date).date() if date else get_today())
        self.check_dt = pd.to_datetime("{} {}".format(self.date, check_time))
        self.check_ts = dt2ts(self.check_dt)

        if stocks:
            self.stocks = stocks
        else:
            df = import_module("jqdata.apis").get_all_securities("stock", date)
            self.stocks = df.index.tolist()
        self._icodes = None

        self._exchanges = {'XSHE', 'XSHG'} & {
            code.rsplit('.')[-1] for code in self.stocks
        }

        if shm_rootdir:
            self._shm_rootdir = shm_rootdir
        else:
            self._shm_rootdir = os.path.join(
                os.getenv("JQ_SHM_ROOT", "/dev/shm/"), "l2tick"
            )

        self._shm_arrays = []
        self.load_shm_lastprice()

    @property
    def icodes(self):
        """Int 类型的股票代码"""
        if not self._icodes:
            self._icodes = [int(code.rsplit('.', 1)[0]) for code in self.stocks]
        return self._icodes

    def _get_lastprice_path(self, exchange):
        return os.path.join(
            self._shm_rootdir, f"LASTPRICE.{exchange}.{self.date}"
        )

    def has_shm_lastprice(self):
        return all(
            os.path.exists(self._get_lastprice_path(exchange))
            for exchange in self._exchanges
        )

    def load_shm_lastprice(self):
        arrays = []
        for exchange in self._exchanges:
            sa_path = self._get_lastprice_path(exchange)
            if not os.path.exists(sa_path):
                continue
            arr = attach_sa("file://" + sa_path, readonly=True)
            arrays.append(arr)
        self._shm_arrays = arrays

    def get_ready_data_percent_by_shm(self):
        data_ready_stock_count = 0
        for arr in self._shm_arrays:
            condition = (np.in1d(arr[:, 0], self.icodes)
                         & (arr[:, 1] >= self.check_ts)
                         & (arr[:, 1] < 2147483647.0))
            valid_arr = arr[condition, :]
            data_ready_stock_count += len(valid_arr)
        return data_ready_stock_count / len(self.stocks)

    def get_ready_data_percent(self):
        # HACK: 交易不活跃时，数据间隔会变大，等待时间会变长，先不等待
        return 1

        if not self.has_shm_lastprice():
            # 如果没有 LASTPRICE 文件则直接返回 1，表示不等待
            return 1

        percent = self.get_ready_data_percent_by_shm()

        if percent == 1:
            return percent

        # 特殊时间段，不好判断数据到达的情况，在过一定时间后强制置为 1
        check_time = self.check_dt.time()
        if datetime.time(9, 25) <= check_time < datetime.time(9, 30):
            if get_now().time() > datetime.time(9, 26):
                percent = 1
        elif datetime.time(11, 30) <= check_time < datetime.time(13, 0):
            if get_now().time() > datetime.time(11, 30, 10):
                percent = 1
        elif check_time >= datetime.time(15):
            if get_now().time() > datetime.time(15, 0, 10):
                percent = 1

        return percent


def get_custom_shm_root():
    return os.environ.get("JQ_SHM_ROOT")


def force_use_shm():
    return os.getenv("JQ_USE_SHM", "") == "1"


def force_no_use_shm():
    return os.getenv("JQ_USE_SHM", "") == "0"


def force_use_feather():
    return os.getenv("JQ_USE_FEATHER", "") == "1"


class Level2Order(PathMixin):

    ORDER_COLUMNS = [
        "OrderID",     # 原始订单号，仅限上交所，深交所始终为 0
        "Time",        # 委托单时间戳
        "UpdateTime",  # 更新时间戳（接收到行情的时间）
        "Side",        # 买卖方向（0委托买入，1委托卖出，2借入，3出借，4未知类型）
        "Price",       # 委托价格
        "Volume",      # 委托数量
        "OrderType",   # 订单类型（1市价单，2限价单，3本方最优先，4未知类型, 5上交所撤单）
    ]
    SHM_ORDER_COLUMNS = ORDER_COLUMNS + [
        "TimeUS",  # 委托单时间戳微秒部分
        "UpdateTimeUS",  # 更新时间戳微秒部分
    ]
    NEW_SHM_ORDER_COLUMNS = SHM_ORDER_COLUMNS + [
        "RawVolume",  # 原始委托量
    ]
    ORDER_USEFUL_CSV_COLUMNS = [
        "OrderID",
        "TickTime",
        "Time",
        "BSFlag",
        "Price",
        "Volume",
        "OrdType",
    ]
    ORDER_CSV_SA_COLUMN_MAPPING = OrderedDict(
        zip(ORDER_USEFUL_CSV_COLUMNS, ORDER_COLUMNS)
    )
    SHM_ORDER_DIR = "/dev/shm/l2order"

    @classmethod
    def get_csv_order_path(cls, code, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        return os.path.join(cls.get_snap_level2_dir(), "order",
                            date.strftime('%Y%m/%Y-%m-%d'), code)

    @classmethod
    def load_order_from_csv(cls, code=None, date=None, path=None,
                            precheck_exists=True):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_csv_order_path(code, date)
        else:
            path_infos = path.split(os.sep)
            date, code = path_infos[-2], path_infos[-1]
            date = datetime.datetime.strptime(date, "%Y-%m-%d").date()

        if precheck_exists and not os.path.exists(path):
            return pd.DataFrame(columns=cls.ORDER_COLUMNS)

        df = pd.read_csv(path, dtype={"OrdType": "str"})

        # 上交所的委托号为 OrderID 字段，深交所为 SeqNum 字段
        if code.endswith("XSHE") and "SeqNum" in df.columns:
            df["OrderID"] = df["SeqNum"]

        csv_sa_column_mapping = cls.ORDER_CSV_SA_COLUMN_MAPPING
        df = df.reindex(columns=list(csv_sa_column_mapping.keys()))
        df = df.rename(columns=csv_sa_column_mapping)

        # 转换时间戳
        date_s = str(date)
        times = pd.to_datetime(date_s + ' ' + df["Time"])
        df["Time"] = times.view(np.int64) / (10 ** 9) - 8 * 3600
        update_times = pd.to_datetime(date_s + ' ' + df["UpdateTime"])
        df["UpdateTime"] = update_times.view(np.int64) / (10 ** 9) - 8 * 3600

        # 转换买卖方向
        bsflags = {"B": 0, "S": 1, "G": 2, "F": 3}
        df["Side"] = df["Side"].map(bsflags).fillna(4)

        # 转换委托类型
        ordertypes = {"1": 1, "2": 2, "U": 3, "D": 5}
        df["OrderType"] = df["OrderType"].map(ordertypes).fillna(4)

        # 市价单委托价格处理成0
        df.loc[df["OrderType"].isin([1, 3]), ["Price"]] = 0

        # 上交所可转债单位调整成张
        if date < datetime.date(2022, 9, 27) and code.endswith("XSHG") and code[:3] in ("110", "111", "113", "118"):
            df["Volume"] *= 10

        return df.astype("float64")

    @classmethod
    def get_shm_root_dir(cls):
        custom_order_shm_root = os.getenv("JQ_ORDER_SHM_ROOT")
        if custom_order_shm_root:
            return custom_order_shm_root
        custom_shm_root = get_custom_shm_root()
        if custom_shm_root:
            return os.path.join(custom_shm_root, "l2order")
        else:
            return cls.SHM_ORDER_DIR

    @classmethod
    def get_sa_order_path(cls, code, date):
        date = pd.to_datetime(date).date()

        # 强制使用共享内存时，直接返回共享内存路径，不再检查是否存在
        if force_use_shm():
            return os.path.join(cls.get_shm_root_dir(), date.strftime('%Y-%m-%d'), code)

        # 仍返回目前不再更新的fastjqdata/l2order-sa，上层函数找不到路径时返回空DataFrame
        if force_no_use_shm():
            return os.path.join(cls.get_fastjqdata_dir(), "l2order-sa", date.strftime('%Y%m/%Y-%m-%d'), code)

        path = None
        if date == datetime.date.today():
            path = os.path.join(cls.get_shm_root_dir(), date.strftime('%Y-%m-%d'), code)

        # if not path or not os.path.exists(path):
        #     path = os.path.join(cls.get_fastjqdata_dir(), "l2order-sa", date.strftime('%Y%m/%Y-%m-%d'), code)
        return path

    @classmethod
    def load_order_from_sa(cls, code=None, date=None, path=None, unified=False,
                           precheck_exists=True):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_sa_order_path(code, date)

        if precheck_exists and not os.path.exists(path):
            return pd.DataFrame(columns=cls.ORDER_COLUMNS, dtype='float64')

        arr = attach_sa('file://' + path, readonly=True)

        if force_use_shm() or path.startswith(cls.get_shm_root_dir()) or date == get_today():
            end_idx = arr[:, 1].searchsorted(MAX_INT32)
            if end_idx <= 0:
                return pd.DataFrame(columns=cls.ORDER_COLUMNS, dtype='float64')
            arr = arr[:end_idx]
            shm_columns = cls.SHM_ORDER_COLUMNS
            new_shm_columns = cls.NEW_SHM_ORDER_COLUMNS
            is_new = len(arr[0]) >= len(new_shm_columns)

            if is_new and not unified:
                # 去除深交所撤单
                if (code and code.endswith('XSHE')) or path.endswith('XSHE'):
                    arr = arr[arr[:, new_shm_columns.index("OrderType")] != 5]
                # 去除上交所主动全部成交的订单
                elif (code and code.endswith('XSHG')) or path.endswith('XSHG'):
                    volumes = arr[:, new_shm_columns.index("Volume")]
                    raw_volumes = arr[:, new_shm_columns.index("RawVolume")]
                    arr = arr[~((volumes == 0) & (raw_volumes > 0))]

            a = np.empty(shape=(len(arr), len(cls.ORDER_COLUMNS)), order='F', dtype='float64')
            for col_index, col_name in enumerate(cls.ORDER_COLUMNS):
                if col_name == 'Price':
                    a[:, col_index] = arr[:, shm_columns.index(col_name)]
                    a[:, col_index] /= PRICE_MULTIPLIER
                elif col_name == 'Time' or col_name == 'UpdateTime':
                    a[:, col_index] = arr[:, shm_columns.index(f'{col_name}US')]
                    a[:, col_index] /= ONE_MILLION
                    a[:, col_index] += arr[:, shm_columns.index(col_name)]
                elif col_name == 'Volume':
                    if is_new and unified:
                        # 上交所 Order 中的委托量是主动成交后剩余的委托量，这里使用原始委托量
                        a[:, col_index] = arr[:, new_shm_columns.index('RawVolume')]
                    else:
                        a[:, col_index] = arr[:, shm_columns.index('Volume')]
                else:
                    a[:, col_index] = arr[:, shm_columns.index(col_name)]

            # if is_new:
            #     needed_col_len = new_col_len
            #     columns = cls.NEW_SHM_ORDER_COLUMNS[:needed_col_len]
            #     df = pd.DataFrame(arr[:end_idx, :needed_col_len],
            #                       columns=columns, dtype="float64", copy=False)
            #     if unified:
            #         if (code and code.endswith('XSHG')) or path.endswith('XSHG'):
            #             # 上交所 Order 中的委托量是主动成交后剩余的委托量，这里替换为原始委托量
            #             df.rename(columns={
            #                 "Volume": "OldVolume", "RawVolume": "Volume"
            #             }, inplace=True)
            #     else:
            #         # 去除深交所撤单
            #         if (code and code.endswith('XSHE')) or path.endswith('XSHE'):
            #             df = df[df.OrderType != 5]
            #         # 去除上交所主动全部成交的订单
            #         elif (code and code.endswith('XSHG')) or path.endswith('XSHG'):
            #             df = df[~((df.Volume == 0) & (df.RawVolume > 0))]
            #             df = df.reset_index(drop=True)
            # else:
            #     needed_col_len = min(arr_col_len, old_col_len)
            #     columns = cls.SHM_ORDER_COLUMNS[:needed_col_len]
            #     df = pd.DataFrame(arr[:end_idx, :needed_col_len],
            #                       columns=columns, dtype="float64", copy=False)

            # df["Price"] = df["Price"] / PRICE_MULTIPLIER
            # if needed_col_len >= old_col_len:
            #     df["Time"] = df["Time"] + (df["TimeUS"] / ONE_MILLION)
            #     df["UpdateTime"] = df["UpdateTime"] + (df["UpdateTimeUS"] / ONE_MILLION)
            # needed_columns = cls.ORDER_COLUMNS
            # df = df.reindex(columns=needed_columns)
            df = pd.DataFrame(a, copy=False, columns=cls.ORDER_COLUMNS)
            if not is_new and unified:
                deal = load_deal(code, date, unified=False)
                _, df = convert_deal_order(deal, df, code, inplace=True)
                if (code and code.endswith('XSHE')) or path.endswith('XSHE'):
                    # 填充深交所市价单的委托价
                    df = fill_szmarket_price(deal, df, code=code, inplace=True)
        else:
            df = pd.DataFrame(arr, columns=cls.ORDER_COLUMNS, copy=True)

        # 市价单委托价格处理成0（NOTE: 实时数据中已经处理，这里不再处理）
        # df.loc[(df["OrderType"] == 1) | (df["OrderType"] == 3), ["Price"]] = 0
        return df

    @classmethod
    def get_npz_order_path(cls, code, date):
        date = pd.to_datetime(date).date()
        nas_root_dir = cls.get_fastjqdata_dir()
        return os.path.join(
            nas_root_dir,
            "l2order-npz",
            date.strftime('%Y%m/%Y-%m-%d'),
            code + ".npz"
        )

    @classmethod
    @lru_cache(maxsize=get_per_factor_process_max_tasks(default=0))
    def load_order_from_npz(cls, code=None, date=None, path=None):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_npz_order_path(code, date)

        loaded = read_npz(path, allow_pickle=True)
        df = pd.DataFrame(data=loaded["data"], columns=loaded["columns"])

        df["Time"] = df["Time"] + (df["TimeUS"] / ONE_MILLION)
        df["UpdateTime"] = df["UpdateTime"] + (df["UpdateTimeUS"] / ONE_MILLION)

        df["Price"] = df["Price"] / PRICE_MULTIPLIER

        df.drop(["TimeUS", "UpdateTimeUS"], axis=1, inplace=True)
        return df.astype("float64")


def load_order(code=None, date=None, path=None, unified=False):
    """ 加载 Level2 逐笔委托数据

    Args：
        code: 标的代码字符串，需带交易所后缀
        date: 加载数据的日期，可以是字符串(YYYY-MM-DD)也可以是datetime.date对象
        path: 从指定文件中加载数据，支持SharedArray 格式、npz格式的文件
            如果指定了path，会分别尝试用npz、SharedArray格式去解析文件，如果路径上的文件不存在会返回空DataFrame。
            如果没指定path：
                如果date == today, 就会先尝试去共享内存中找SharedArray格式的数据。
                如果没找到数据，就会再去历史npz数据中寻找，如果没找到文件，就会返回空DataFrame。
        unified: 是否返回统一格式后的数据，统一后的格式参考 convert_deal_order 函数

    Notice:
        1. 不指定path参数的时候，必须指定code、date
        2. 指定环境变量JQ_USE_SHM时的行为：
            * os.environ["JQ_USE_SHM"]="1"时，强制使用共享内存中date这天的数据，数据不存在时，返回空DataFrame
            * os.environ["JQ_USE_SHM"]="0"时，强制禁用共享内存，此时会去npz中获取这天的数据，数据不存在时，返回空DataFrame
            * 不指定JQ_USE_SHM或着JQ_USE_SHM为其他值时，此环境变量无效，此时会根据date的值来选择加载数据：
                * 如果date = 当日，使用共享内存数据，如果共享内存中数据文件不存在，再尝试去npz中加载数据
                * 如果date != 当日，尝试去npz中加载数据。
                * 如果最终仍找不到数据文件，返回空DataFrame
        3. 指定环境变量JQ_SHM_ROOT的时候，会使用指定的路径作为共享内存数据的根路径，不指定时默认 /dev/shm

    Return: pd.DataFrame
        dtype: float64
        columns:
            OrderID: 交易所原始订单号
            Time: 委托单时间戳，精确到微秒，建议使用如下的方式转化时间
                import ft_common_base
                df['Time'] = ft_common_base.ts2dt(df.Time)
            UpdateTime: 更新时间戳（接收到行情的时间，精确到微秒）
            Side: 买卖方向
                0: 委托买入
                1: 委托卖出
                2: 借入
                3: 出借
                4: 未知类型
            Price: 委托价格
            Volume: 委托数量
            OrderType: 订单类型
                1: 市价单
                2: 限价单
                3: 本方最优先
                4: 未知类型
                5: 上交所撤单
    """
    if path:
        if not os.path.exists(path):
            return pd.DataFrame(columns=Level2Order.ORDER_COLUMNS)

        if path.endswith(".npz"):
            return Level2Order.load_order_from_npz(path=path)
        else:
            return Level2Order.load_order_from_sa(path=path)

    if not (code and date):
        raise ValueError("不指定path参数时，必须指定code和date参数")

    date = pd.to_datetime(date).date()

    if unified:
        if force_use_shm() or (not force_no_use_shm() and date == get_today()):
            path = Level2Order.get_sa_order_path(code, date)
            try:
                return Level2Order.load_order_from_sa(
                    code, date, path=path, unified=True, precheck_exists=False,
                )
            except FileNotFoundError:
                pass

        _, order = _load_converted_deal_order(code, date, fill_sz=True)
        return order

    # 强制使用共享内存内存时，找不到数据文件，直接返回空DataFrame
    if force_use_shm():
        path = Level2Order.get_sa_order_path(code, date)
        try:
            return Level2Order.load_order_from_sa(
                code, date, path=path, precheck_exists=False
            )
        except FileNotFoundError:
            return pd.DataFrame(columns=Level2Order.ORDER_COLUMNS, dtype='float64')

    # 强制禁用共享内存时，找不到npz数据，返回空DataFrame
    if force_no_use_shm():
        npz_path = Level2Order.get_npz_order_path(code, date)
        try:
            return Level2Order.load_order_from_npz(code, date, path=npz_path)
        except FileNotFoundError:
            return pd.DataFrame(columns=Level2Order.ORDER_COLUMNS, dtype='float64')

    if date == get_today():
        path = Level2Order.get_sa_order_path(code, date)
        try:
            return Level2Order.load_order_from_sa(
                code, date, path=path, precheck_exists=False
            )
        except FileNotFoundError:
            pass

    npz_path = Level2Order.get_npz_order_path(code, date)
    try:
        return Level2Order.load_order_from_npz(code, date, path=npz_path)
    except FileNotFoundError:
        pass

    # sa_path = Level2Order.get_sa_order_path(code, date)
    # if os.path.exists(sa_path):
    #     return Level2Order.load_order_from_sa(path=sa_path)
    #
    # csv_path = Level2Order.get_csv_order_path(code, date)
    # if os.path.exists(csv_path):
    #     return Level2Order.load_order_from_csv(path=csv_path)

    return pd.DataFrame(columns=Level2Order.ORDER_COLUMNS, dtype='float64')


class Level2Deal(PathMixin):

    # 逐笔成交数据列
    DEAL_COLUMNS = [
        "DealID",       # 成交ID，仅为一个行情记录号，顺序递增
        "Time",         # 成交单时间戳
        "UpdateTime",   # 更新时间戳（接收到行情的时间）
        "SaleOrderID",  # 卖方委托索引
        "BuyOrderID",   # 买方委托索引
        "Side",         # 买卖方向（0主动买，1主动卖，4深交所撤单，10未知，上交所集合竞价发送）
        "Price",        # 成交价格
        "Volume",       # 成交数量
        "Money",        # 成交金额
    ]
    DEAL_USEFUL_CSV_COLUMNS = [
        "SeqNum",
        "TickTime",
        "Time",
        "AskSeqNum",
        "BidSeqNum",
        "BSFlag",
        "Price",
        "Volume",
        "Money",
    ]
    DEAL_CSV_SA_COLUMN_MAPPING = OrderedDict(
        zip(DEAL_USEFUL_CSV_COLUMNS, DEAL_COLUMNS)
    )

    # 因为历史原因，逐笔成交数据存储使用了quanxi sa2的格式存储
    SHM_DEAL_DIR = "/dev/shm/quanxi"
    SHM_DEAL_COLUMNS = [
        'DealID', # 全息数据ID
        'Time', # 逐笔成交数据的时间戳（不包含微秒）
        'UpdateTime', # 实际收到逐笔成交数据的时间戳（不包含微秒）. 实时数据有意义, 历史数据可能不准.
        'Side', # 是否是卖, 1是卖, 0是买
        'Price', # 成交价格 * 10000
        'Volume', # 成交量
        'SaleOrderID', # 卖单ID
        'SaleOrderPrice', # 卖单挂单价格 * 10000
        'SaleOrderVolume',  # 卖单挂单量
        'BuyOrderID', # 买单ID
        'BuyOrderPrice', # 买单挂单价格 * 10000
        'BuyOrderVolume', # 买单挂单量
        'TimeUS', # Time小数部分，单位微秒
        'UpdateTimeUS', # UpdateTime小数部分，单位微秒
    ]

    @classmethod
    def get_shm_root_dir(cls):
        # 实时共享内存 quanxi 和 deal 为同一份数据
        custom_quanxi_shm_root = os.getenv("JQ_QUANXI_SHM_ROOT")
        if custom_quanxi_shm_root:
            return custom_quanxi_shm_root
        custom_deal_shm_root = os.getenv("JQ_DEAL_SHM_ROOT")
        if custom_deal_shm_root:
            return custom_deal_shm_root
        custom_shm_root = get_custom_shm_root()
        if custom_shm_root:
            return os.path.join(custom_shm_root, "quanxi")
        else:
            return cls.SHM_DEAL_DIR

    @classmethod
    def get_csv_deal_path(cls, code, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        return os.path.join(cls.get_snap_level2_dir(), "deal",
                            date.strftime('%Y%m/%Y-%m-%d'), code)

    @classmethod
    def load_deal_from_csv(cls, code=None, date=None, path=None,
                           precheck_exists=True):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_csv_deal_path(code, date)
        else:
            path_infos = path.split(os.sep)
            date, code = path_infos[-2], path_infos[-1]
            date = datetime.datetime.strptime(date, "%Y-%m-%d").date()

        if precheck_exists and not os.path.exists(path):
            return pd.DataFrame(columns=cls.DEAL_COLUMNS)

        df = pd.read_csv(path, dtype={"BSFlag": "str"})

        csv_sa_column_mapping = cls.DEAL_CSV_SA_COLUMN_MAPPING
        df = df.reindex(columns=list(csv_sa_column_mapping.keys()))
        df = df.rename(columns=csv_sa_column_mapping)

        # 转换时间戳
        date_s = str(date)
        times = pd.to_datetime(date_s + ' ' + df["Time"])
        df["Time"] = times.view(np.int64) / (10 ** 9) - 8 * 3600
        update_times = pd.to_datetime(date_s + ' ' + df["UpdateTime"])
        df["UpdateTime"] = update_times.view(np.int64) / (10 ** 9) - 8 * 3600

        # 转换买卖方向
        bsflags = {'B': 0, 'S': 1, '4': 4, 'N': 10}
        df["Side"] = df["Side"].map(bsflags).fillna(10)

        # 深交所撤单的成交价格确保为0，上交所撤单在逐笔委托中，撤单中的Price为被撤委托的委托价格，就不处理了。
        df.loc[df["Side"] == 4, ["Price"]] = 0

        # 上交所可转债单位调整成张
        if date < datetime.date(2022, 9, 27) and code.endswith("XSHG") and code[:3] in ("110", "111", "113", "118"):
            df["Volume"] *= 10

        return df.astype("float64")

    @classmethod
    def get_sa_deal_path(cls, code, date):
        date = pd.to_datetime(date).date()

        # 强制使用共享内存时，直接返回共享内存路径
        if force_use_shm():
            return os.path.join(cls.get_shm_root_dir(), date.strftime('%Y-%m-%d'), code)

        # 仍返回目前不再更新的fastjqdata/l2deal-sa，上层函数找不到路径时返回空DataFrame
        if force_no_use_shm():
            return os.path.join(cls.get_fastjqdata_dir(), "l2deal-sa", date.strftime('%Y%m/%Y-%m-%d'), code)

        path = None
        if date == datetime.date.today():
            path = os.path.join(cls.get_shm_root_dir(), date.strftime('%Y-%m-%d'), code)

        # if not path or not os.path.exists(path):
        #     path = os.path.join(cls.get_fastjqdata_dir(), "l2deal-sa", date.strftime('%Y%m/%Y-%m-%d'), code)
        return path

    @classmethod
    @lru_cache(None)
    def _get_shm_deal_version(cls, rootdir):
        version_file = os.path.join(rootdir, "VERSION")
        default_version = (1, 0, 0)
        if not os.path.exists(version_file):
            return default_version

        try:
            with open(version_file) as fp:
                version_infos = fp.read().strip().split()
            if len(version_infos) < 2:
                return default_version

            version_update_date = datetime.datetime.strptime(
                version_infos[1], "%Y%m%d"
            ).date()
            if version_update_date < datetime.date.today():
                return default_version

            return tuple(int(item) for item in version_infos[0].split('.'))[:3]
        except Exception as ex:
            logger.warning("failed to parse shm deal version: %s", ex)
            return default_version

    @classmethod
    def is_new_deal(cls):
        is_new_deal_order = bool(os.getenv("IS_JQ_NEW_DEAL_ORDER"))
        if not is_new_deal_order:
            shm_deal_version = cls._get_shm_deal_version(cls.get_shm_root_dir())
            is_new_deal_order = shm_deal_version[0] >= 2
        return is_new_deal_order

    @classmethod
    def load_deal_from_sa(cls, code=None, date=None, path=None, unified=False,
                          precheck_exists=True):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_sa_deal_path(code, date)

        if precheck_exists and not os.path.exists(path):
            return pd.DataFrame(columns=cls.DEAL_COLUMNS, dtype='float64')

        arr = attach_sa('file://' + path, readonly=True)

        if force_use_shm() or path.startswith(cls.get_shm_root_dir()) or date == datetime.date.today():
            end_idx = arr[:, 1].searchsorted(MAX_INT32)
            if end_idx <= 0:
                return pd.DataFrame(columns=cls.DEAL_COLUMNS, dtype='float64')
            # col_length = min(len(arr[0]), len(cls.SHM_DEAL_COLUMNS))
            arr = arr[:end_idx]
            if cls.is_new_deal():
                if unified:
                    arr = arr[arr[:, cls.SHM_DEAL_COLUMNS.index('Side')] != 4]

            # a = np.array(arr[:end_idx, :col_length], order='F', dtype='float64', copy=True)
            a = np.empty(shape=(len(arr), len(cls.DEAL_COLUMNS)), order='F', dtype='float64')
            price_col_index = None
            for col_index, col_name in enumerate(cls.DEAL_COLUMNS):
                if col_name == 'Price':
                    a[:, col_index] = arr[:, cls.SHM_DEAL_COLUMNS.index(col_name)]
                    a[:, col_index] /= PRICE_MULTIPLIER
                    price_col_index = col_index
                elif col_name == 'Money':
                    a[:, col_index] = a[:, price_col_index]
                    a[:, col_index] *= arr[:, cls.SHM_DEAL_COLUMNS.index('Volume')]
                elif col_name == 'Time' or col_name == "UpdateTime":
                    a[:, col_index] = arr[:, cls.SHM_DEAL_COLUMNS.index(f'{col_name}US')]
                    a[:, col_index] /= ONE_MILLION
                    a[:, col_index] += arr[:, cls.SHM_DEAL_COLUMNS.index(col_name)]
                else:
                    a[:, col_index] = arr[:, cls.SHM_DEAL_COLUMNS.index(col_name)]

            # df = pd.DataFrame(a, columns=cls.SHM_DEAL_COLUMNS[:col_length],
            #                   copy=False)
            # df["Price"] = df["Price"] / PRICE_MULTIPLIER
            # df["Money"] = df["Price"] * df["Volume"]
            # if col_length == len(cls.SHM_DEAL_COLUMNS):
            #     df["Time"] = df["Time"] + (df["TimeUS"] / ONE_MILLION)
            #     df["UpdateTime"] = df["UpdateTime"] + (df["UpdateTimeUS"] / ONE_MILLION)
            # df = df.reindex(columns=cls.DEAL_COLUMNS)
            df = pd.DataFrame(a, copy=False, columns=cls.DEAL_COLUMNS)
        else:
            df = pd.DataFrame(arr, columns=cls.DEAL_COLUMNS)

        if cls.is_new_deal():
            if unified:
                # 新数据中，深交所和上交所都有撤单，直接去除（旧数据只有深交所有撤单的数据）
                # df = df[df.Side != 4].reset_index(drop=True)
                pass
            else:
                if (code and code.endswith('XSHG')) or path.endswith('XSHG'):
                    # 旧数据中，上交所没有撤单
                    df = df[df.Side != 4].reset_index(drop=True)
                    # 还原上交所集合竞价期间的 Side 为 10
                    df.loc[df.Time < dt2ts(f'{date} 09:30:00'), 'Side'] = 10
                    df.loc[df.Time >= dt2ts(f'{date} 14:57:00'), 'Side'] = 10
        else:
            if unified:
                if (code and code.endswith('XSHE')) or path.endswith('XSHE'):
                    # 去除深交所撤单（撤单被挪到了 order 中）
                    df = df[df.Side != 4].reset_index(drop=True)
                elif (code and code.endswith('XSHG')) or path.endswith('XSHG'):
                    # 将上交所集合竞价产生的买卖方向为 10 的成交, 按照深交所的方式进行还原
                    #   BuyOrderID > SaleOrderID --> Side=0
                    #   BuyOrderID < SaleOrderID --> Side=1
                    unknown_side_deal = df[df.Side == 10]
                    if not unknown_side_deal.empty:
                        df.loc[unknown_side_deal.index, 'Side'] = (
                            unknown_side_deal.BuyOrderID < unknown_side_deal.SaleOrderID
                        ).astype(int)

        return df

    @classmethod
    def get_npz_deal_path(cls, code, date):
        date = pd.to_datetime(date).date()
        nas_root_dir = cls.get_fastjqdata_dir()
        return os.path.join(
            nas_root_dir,
            "l2deal-npz",
            date.strftime('%Y%m/%Y-%m-%d'),
            code + ".npz"
        )

    @classmethod
    @lru_cache(maxsize=get_per_factor_process_max_tasks(default=0))
    def load_deal_from_npz(cls, code=None, date=None, path=None):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_npz_deal_path(code, date)

        loaded = read_npz(path, allow_pickle=True)
        df = pd.DataFrame(data=loaded["data"], columns=loaded["columns"])

        df["Time"] = df["Time"] + (df["TimeUS"] / ONE_MILLION)
        df["UpdateTime"] = df["UpdateTime"] + (df["UpdateTimeUS"] / ONE_MILLION)
        df["Price"] = df["Price"] / PRICE_MULTIPLIER
        df["Money"] = df["Price"] * df["Volume"]

        df.drop(["TimeUS", "UpdateTimeUS"], axis=1, inplace=True)
        return df.astype("float64")


def load_deal(code=None, date=None, path=None, unified=False):
    """加载 Level2 逐笔成交数据

    Args：
        code: 标的代码字符串，需带交易所后缀
        date: 加载数据的日期，可以是字符串(YYYY-MM-DD)也可以是datetime.date对象
        path: 从指定文件中加载数据，支持 SharedArray格式、npz格式的文件
            如果指定了path，会分别尝试用npz、SharedArray格式去解析文件，如果路径上的文件不存在，就会返回空DataFrame
            如果没指定path，会分别尝试从机器上寻找npz、SharedArray、csv数据，然后去解析，如果都没找到文件，就会返回空DataFrame。
        unified: 是否返回统一格式后的数据，统一后的格式参考 convert_deal_order 函数

    Notice:
        1. 不指定path参数的时候，必须指定code、date
        2. 指定环境变量JQ_USE_SHM时的行为：
            * os.environ["JQ_USE_SHM"]="1"时，强制使用共享内存中date这天的数据，数据不存在时，返回空DataFrame
            * os.environ["JQ_USE_SHM"]="0"时，强制禁用共享内存，此时会去npz中获取这天的数据，数据不存在时，返回空DataFrame
            * 不指定JQ_USE_SHM或着JQ_USE_SHM为其他值时，此环境变量无效，此时会根据date的值来选择加载数据：
                * 如果date = 当日，使用共享内存数据，如果共享内存中数据文件不存在，再尝试去npz中加载数据
                * 如果date != 当日，尝试去npz中加载数据。
                * 如果最终仍找不到数据文件，返回空DataFrame
        3. 指定环境变量JQ_SHM_ROOT的时候，会使用指定的路径作为共享内存数据的根路径，不指定时默认 /dev/shm

    Return: pd.DataFrame
        dtype: float64
        columns:
            DealID: 成交ID，仅为一个行情记录号，顺序递增
            OrderID: 交易所原始订单号
            Time: 委托单时间戳，精确到微秒，建议使用如下的方式转化时间
                import ft_common_base
                df['Time'] = ft_common_base.ts2dt(df.Time)
            UpdateTime: 更新时间戳（接收到行情的时间，精确到微秒）
            SaleOrderID: 卖方委托索引
            BuyOrderID: 买方委托索引
            Side: 买卖方向
                0: 主动买
                1: 主动卖
                4: 撤单，仅限深交所
                10: 未知，上交所集合竞价发送
            Price: 成交价格
            Volume: 成交数量
            Money: 成交金额
    """
    if path:
        if not os.path.exists(path):
            return pd.DataFrame(columns=Level2Deal.DEAL_COLUMNS)

        if path.endswith(".npz"):
            return Level2Deal.load_deal_from_npz(path=path)
        else:
            return Level2Deal.load_deal_from_sa(code, date, path=path)

    if not (code and date):
        raise ValueError("不指定path参数时，必须指定code和date参数")

    date = pd.to_datetime(date).date()

    if unified:
        if force_use_shm() or (not force_no_use_shm() and date == get_today()):
            path = Level2Deal.get_sa_deal_path(code, date)
            try:
                return Level2Deal.load_deal_from_sa(
                    code, date, path=path, unified=True, precheck_exists=False,
                )
            except FileNotFoundError:
                pass

        deal, _ = _load_converted_deal_order(code, date, fill_sz=True)
        return deal

    # 强制使用共享内存时，找不到数据文件，返回空DataFrame
    if force_use_shm():
        path = Level2Deal.get_sa_deal_path(code, date)
        try:
            return Level2Deal.load_deal_from_sa(
                code, date, path=path, precheck_exists=False
            )
        except FileNotFoundError:
            return pd.DataFrame(columns=Level2Deal.DEAL_COLUMNS, dtype='float64')

    # 强制禁用共享内存时，找不到npz数据，返回空DataFrame
    if force_no_use_shm():
        npz_path = Level2Deal.get_npz_deal_path(code, date)
        try:
            return Level2Deal.load_deal_from_npz(path=npz_path)
        except FileNotFoundError:
            return pd.DataFrame(columns=Level2Deal.DEAL_COLUMNS, dtype='float64')

    if date == get_today():
        path = Level2Deal.get_sa_deal_path(code, date)
        try:
            return Level2Deal.load_deal_from_sa(
                code, date, path=path, precheck_exists=False
            )
        except FileNotFoundError:
            pass

    npz_path = Level2Deal.get_npz_deal_path(code, date)
    try:
        return Level2Deal.load_deal_from_npz(path=npz_path)
    except FileNotFoundError:
        pass

    # sa_path = Level2Deal.get_sa_deal_path(code, date)
    # if os.path.exists(sa_path):
    #     return Level2Deal.load_deal_from_sa(path=sa_path)
    #
    # csv_path = Level2Deal.get_csv_deal_path(code, date)
    # if os.path.exists(csv_path):
    #     return Level2Deal.load_deal_from_csv(path=csv_path)

    return pd.DataFrame(columns=Level2Deal.DEAL_COLUMNS, dtype='float64')


class FutCmb(PathMixin):
    """期货组合合约（Future Combination Contract）"""

    _daily_contracts = {}

    @classmethod
    def get_daily_sa_futcmb_dir(cls, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        return os.path.join(
            cls.get_fastjqdata_dir(), "level2/futcmb",
            date.strftime("%Y%m/%Y-%m-%d"),
        )

    @classmethod
    def get_daily_npz_futcmb_dir(cls, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        return os.path.join(
            cls.get_fastjqdata_dir(), "level2/futcmb-npz",
            date.strftime("%Y%m/%Y-%m-%d"),
        )

    @classmethod
    def get_sa_futcmb_path(cls, code, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        return os.path.join(
            cls.get_fastjqdata_dir(), "level2/futcmb",
            date.strftime("%Y%m/%Y-%m-%d"), code,
        )

    @classmethod
    def get_npz_futcmb_path(cls, code, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        return os.path.join(
            cls.get_fastjqdata_dir(), "level2/futcmb-npz",
            date.strftime("%Y%m/%Y-%m-%d"), code + ".npz",
        )

    @classmethod
    def get_futcmb_contracts(cls, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        if date not in cls._daily_contracts:
            data_dir = cls.get_daily_npz_futcmb_dir(date)
            if os.path.isdir(data_dir):
                suffixes = tuple(FUTURE_EXCHANGES)
                contracts = []
                for name in os.listdir(data_dir):
                    code = name.replace(".npz", "")
                    if code.endswith(suffixes):
                        contracts.append(code)
                cls._daily_contracts[date] = contracts
        return cls._daily_contracts.get(date, [])

    @classmethod
    def load_futcmb_from_sa(cls, code=None, date=None, path=None):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_sa_futcmb_path(code, date)
        df = Level2Tick.load_l2ticks_from_sa(code=code, path=path)
        return df

    @classmethod
    def load_futcmb_from_npz(cls, code=None, date=None, path=None):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_npz_futcmb_path(code, date)
        df = Level2Tick.load_l2ticks_from_npz(code=code, path=path)
        return df


def get_futcmb_contracts(date):
    """获取期货指定日期组合合约列表

    合约命名规则为 组合合约类型-第一腿合约-第二腿合约，再加上交易所后缀
    组合合约类型包括：SP-跨期套利，SPC-跨品种套利

    >>> get_futcmb_contracts("2022-07-18")
    ['SP-A2209-A2301.XDCE',
     'SP-AP2210-AP2211.XZCE',
     ...
     'SPC-Y2209-P2209.XDCE',
     'SPC-Y2301-P2301.XDCE']
    """
    return FutCmb.get_futcmb_contracts(date)


def load_futcmb(code=None, date=None, path=None):
    """加载期货组合合约行情

    参数：
        code: 组合合约，合约命名规则为 组合合约类型-第一腿合约-第二腿合约，再加上交易所后缀
        date: 加载数据的日期
        path: 从指定文件中加载数据，如果文件名不是标的名时，还需要指定 code 参数
              与 code 和 date 参数二选一，仅指定 code 和 date 时会自动搜索数据文件

    返回 pd.DataFrame，数据列与 level2 tick 一致
    """
    return FutCmb.load_futcmb_from_npz(code=code, date=date, path=path)


class FutOrderQ(PathMixin):
    """期货最优价十笔委托"""

    FUTORDERQ_COLUMNS = [
        "time",  # 时间戳
        'a_p',   # 卖最优价格, 基本定单中的卖方向的最优价格
        # 卖委托量, 最优价上，按时间排序的前 10 笔委托
        'a1_q', 'a2_q', 'a3_q', 'a4_q', 'a5_q',
        'a6_q', 'a7_q', 'a8_q', 'a9_q', 'a10_q',
        'b_p',   # 买最优价格, 基本定单中的买方向的最优价格
        # 买委托量, 最优价上，按时间排序的前 10 笔委托
        'b1_q', 'b2_q', 'b3_q', 'b4_q', 'b5_q',
        'b6_q', 'b7_q', 'b8_q', 'b9_q', 'b10_q',
    ]

    @classmethod
    def get_sa_futorderq_path(cls, code, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        return os.path.join(
            cls.get_fastjqdata_dir(), "level2/futorderq",
            date.strftime("%Y%m/%Y-%m-%d"), code,
        )

    @classmethod
    def get_npz_futorderq_path(cls, code, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        return os.path.join(
            cls.get_fastjqdata_dir(), "level2/futorderq-npz",
            date.strftime("%Y%m/%Y-%m-%d"), code + ".npz",
        )

    @classmethod
    def load_futorderq_from_sa(cls, code=None, date=None, path=None):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_sa_futorderq_path(code, date)

        if not os.path.exists(path):
            return pd.DataFrame(columns=cls.FUTORDERQ_COLUMNS)

        arr = attach_sa('file://' + path, readonly=True)
        df = pd.DataFrame(arr, columns=cls.FUTORDERQ_COLUMNS)
        return df

    @classmethod
    def load_futorderq_from_npz(cls, code=None, date=None, path=None):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_npz_futorderq_path(code, date)

        columns = cls.FUTORDERQ_COLUMNS
        if not os.path.exists(path):
            return pd.DataFrame(columns=columns)

        loaded = np.load(path, allow_pickle=True)
        try:
            columns = loaded["columns"]
        except KeyError:
            pass
        return pd.DataFrame(data=loaded["data"], columns=columns)


def load_futorderq(code=None, date=None, path=None):
    """加载期货最优价十笔委托数据

    参数：
        code: 标的，需带交易所后缀
        date: 加载数据的日期
        path: 从指定文件中加载数据，如果文件名不是标的名时，还需要指定 code 参数
              与 code 和 date 参数二选一，仅指定 code 和 date 时会自动搜索数据文件

    返回 pd.DataFrame，数据列为：
        time: 时间戳
        a_p: 卖最优价格, 基本定单中的卖方向的最优价格
        a1_q~a10_q: 卖委托量, 最优价上，按时间排序的前 10 笔委托
        b_p: 买最优价格, 基本定单中的买方向的最优价格
        b1_q~b10_q: 买委托量, 最优价上，按时间排序的前 10 笔委托
    """
    return FutOrderQ.load_futorderq_from_npz(code=code, date=date, path=path)


class FutTQS(PathMixin):
    """期货成交量统计数据"""

    FUTTQS_COLUMNS = [
        'time',  # 时间戳
        'price1',  # 价格
        'buy_open_volume1', 'buy_close_volume1',  # 买开数量/买平数量
        'sell_open_volume1', 'sell_close_volume1',  # 卖开数量/卖平数量
        'price2',
        'buy_open_volume2', 'buy_close_volume2',
        'sell_open_volume2', 'sell_close_volume2',
        'price3',
        'buy_open_volume3', 'buy_close_volume3',
        'sell_open_volume3', 'sell_close_volume3',
        'price4',
        'buy_open_volume4', 'buy_close_volume4',
        'sell_open_volume4', 'sell_close_volume4',
        'price5',
        'buy_open_volume5', 'buy_close_volume5',
        'sell_open_volume5', 'sell_close_volume5'
    ]

    @classmethod
    def get_sa_futtqs_path(cls, code, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        return os.path.join(
            cls.get_fastjqdata_dir(), "level2/futtqs",
            date.strftime("%Y%m/%Y-%m-%d"), code,
        )

    @classmethod
    def get_npz_futtqs_path(cls, code, date):
        if not isinstance(date, datetime.date):
            date = pd.to_datetime(date).date()
        return os.path.join(
            cls.get_fastjqdata_dir(), "level2/futtqs-npz",
            date.strftime("%Y%m/%Y-%m-%d"), code + ".npz",
        )

    @classmethod
    def load_futtqs_from_sa(cls, code=None, date=None, path=None):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_sa_futtqs_path(code, date)

        if not os.path.exists(path):
            return pd.DataFrame(columns=cls.FUTTQS_COLUMNS)

        arr = attach_sa('file://' + path, readonly=True)
        df = pd.DataFrame(arr, columns=cls.FUTTQS_COLUMNS)
        return df

    @classmethod
    def load_futtqs_from_npz(cls, code=None, date=None, path=None):
        if not path:
            assert code and date, "不指定 path 参数时，code 和 date 参数不能为空"
            path = cls.get_npz_futtqs_path(code, date)

        columns = cls.FUTTQS_COLUMNS
        if not os.path.exists(path):
            return pd.DataFrame(columns=columns)

        loaded = np.load(path, allow_pickle=True)
        try:
            columns = loaded["columns"]
        except KeyError:
            pass
        return pd.DataFrame(data=loaded["data"], columns=columns)


def load_futtqs(code=None, date=None, path=None):
    """加载期货成交量统计数据

    参数：
        code: 标的，需带交易所后缀
        date: 加载数据的日期
        path: 从指定文件中加载数据，如果文件名不是标的名时，还需要指定 code 参数
              与 code 和 date 参数二选一，仅指定 code 和 date 时会自动搜索数据文件

    返回 pd.DataFrame，数据列为：
        time: 时间戳，建议使用如下的方式转化时间
            import ft_common_base
            df['time'] = ft_common_base.ts2dt(df.time)
        price1~price5: 价格，成交量第一大的价位区间
            将涨跌停板范围按照 3*tick 划分成多个子区间，
            根据区间内基本定单产生的成交量 降序排列，价格为区间的下限
        buy_open_volume1~buy_open_volume5: 买开数量
            [价格 N，价格 N+2*tick] 价格区间内的买开委托 的成交量
        buy_close_volume1~buy_close_volume5: 买平数量,
            [价格 N，价格 N+2*tick] 价格区间内的买平委托 的成交量
        sell_open_volume1~sell_open_volume5: 卖开数量,
            [价格 N，价格 N+2*tick] 价格区间内的卖开委托 的成交量
        sell_close_volume1~sell_close_volume5: 卖平数量
            [价格 N，价格 N+2*tick] 价格区间内的卖平委托 的成交量
    """
    return FutTQS.load_futtqs_from_npz(code=code, date=date, path=path)


def is_sorted(arr):
    return all(np.diff(arr) >= 0)


def load_npz(path):
    loaded = np.load(path, allow_pickle=True)
    return pd.DataFrame(data=loaded["data"], columns=loaded["columns"])


@njit
def merge_and_sort_numpy_by_numba(df1_array, df2_array):
    num_cols = df1_array.shape[1]
    result_array = np.empty((len(df1_array) + len(df2_array), num_cols), dtype=df1_array.dtype)
    i = j = k = 0

    # 按照3个排序字段进行归并排序
    while i < len(df1_array) and j < len(df2_array):
        # 比较三个排序字段
        if (df1_array[i, 0], df1_array[i, 1], df1_array[i, 2]) <= (df2_array[j, 0], df2_array[j, 1], df2_array[j, 2]):
            result_array[k] = df1_array[i]
            i += 1
        else:
            result_array[k] = df2_array[j]
            j += 1
        k += 1

    # 处理剩余数据
    while i < len(df1_array):
        result_array[k] = df1_array[i]
        i += 1
        k += 1
    while j < len(df2_array):
        result_array[k] = df2_array[j]
        j += 1
        k += 1

    return result_array


def is_mono_tonic(df, sort_by):
    length = len(sort_by)
    flag = None
    if length == 1:
        flag = (df[sort_by[0]].shift() <= df[sort_by[0]])
    else:
        for i in range(length - 1, -1, -1):
            if i == length - 1:
                flag = df[sort_by[i]].shift() <= df[sort_by[i]]
            else:
                flag = (df[sort_by[i]].shift() < df[sort_by[i]]) | ((df[sort_by[i]].shift() == df[sort_by[i]]) & flag)
    return flag.iloc[1:].all()


def merge_and_sort_order_deal(order_df, deal_df, sort_by):
    if not sort_by:
        return pd.concat([order_df, deal_df], ignore_index=True, sort=False)
    sorted_cols = ['Time', 'OrderID', 'OrderType', 'UpdateTime', 'Side', 'Price', 'Volume']
    if not is_mono_tonic(order_df, sort_by):
        order_df.sort_values(sort_by, inplace=True)
    if not is_mono_tonic(deal_df, sort_by):
        deal_df.sort_values(sort_by, inplace=True)
    order = order_df[sorted_cols]
    deal_df = deal_df[sorted_cols]
    order_numpy = merge_and_sort_numpy_by_numba(order.values, deal_df.values)
    order = pd.DataFrame(order_numpy, columns=sorted_cols)
    return order


class Level2OrderQueue(PathMixin):

    ORDERQUEUE_COLUMNS = [
        'time', 'side', 'price', 'volume', 'order_count'
    ] + ["volume%s" % i for i in range(1, 51)]

    @classmethod
    def get_orderqueue_npz_path(cls, code, date):
        return os.path.join(
            cls.get_fastjqdata_dir(),
            "l2orderqueue-npz",
            date.strftime("%Y%m/%Y-%m-%d"),
            code + ".npz"
        )

    @classmethod
    def load_orderqueue(cls, code, date):
        path = cls.get_orderqueue_npz_path(code, date)
        if not os.path.exists(path):
            return pd.DataFrame(columns=cls.ORDERQUEUE_COLUMNS)
        loaded = read_npz(path, allow_pickle=True)
        df = pd.DataFrame(data=loaded["data"], columns=loaded["columns"])
        if hasattr(df, 'sort_values'):
            df = df.sort_values(["time", "side"])
        else:
            df = df.sort(["time", "side"])

        # 去掉无效的数据
        ts_start = time.mktime(datetime.datetime.combine(date, datetime.time(9, 15)).timetuple())
        if code.startswith("3") or code.startswith("68"):
            ts_end = time.mktime(datetime.datetime.combine(date, datetime.time(15, 30, 30)).timetuple())
        else:
            ts_end = time.mktime(datetime.datetime.combine(date, datetime.time(15, 0, 30)).timetuple())
        return df[(df["time"] >= ts_start) & (df["time"] <= ts_end)].reset_index(drop=True)


def load_orderqueue(code, date):
    """ 获取指定标的某天的委托队列数据

    Args:
        code: 标的，需带交易所后缀
        date: 加载数据的日期，支持YYYY-MM-DD、YYYYMMDD字符串 或 datetime.date类型对象

    Return:
        Pd.DataFrame：
            index: RangeIndex(0, n)
            columns:
                time: 交易所下发的数据时间戳
                      建议使用如下的方式转化时间：
                        import ft_common_base
                        df['time'] = ft_common_base.ts2dt(df.time)
                side: 委托队列对应的委托方向. 0: 委托买、1: 委托卖
                price: 委托队列的委托价格
                volume: 委托队列的总委托量（单位：股），order_count > 50时，volume > sum(volume1 + ... + volume50).
                order_count: 委托队列中的委托个数，可以大于50（不过委托队列只展示前50个委托的委托量）
                volume1~volume50: 委托队列中前50笔委托的委托量（单位：股），不存在委托的使用Nan填充委托量
    """
    date = pd.to_datetime(date).date()
    return Level2OrderQueue.load_orderqueue(code, date)


def convert_sz_deal_order(deal, order, sort_by=['Time', 'OrderID', 'OrderType'], fast_sort=True):
    """转换深交所逐笔数据到统一格式"""
    # 还原深交所撤单到 order 中
    cancel_order = deal.loc[
        deal.Side == 4,
        ['Time', 'DealID', 'UpdateTime', 'SaleOrderID', 'BuyOrderID', 'Volume']
    ]
    # 如果没有撤单，则不需要再处理，直接返回
    if cancel_order.empty:
        return deal, order

    cancel_order.index = pd.Index(
        cancel_order.SaleOrderID + cancel_order.BuyOrderID,
        name="OrderID"
    )
    cancel_order['OrderType'] = 5
    cancel_order['Price'] = order.set_index("OrderID").Price
    cancel_order['Side'] = 1.0
    cancel_order.loc[cancel_order.SaleOrderID == 0, 'Side'] = 0.0
    cancel_order = cancel_order.reset_index(drop=False)[[
        "OrderID", "Time", "UpdateTime", "Side", "Price", "Volume",
        "OrderType", "DealID",
    ]]
    # if fast_sort:
    #     order = merge_and_sort_order_deal(order, cancel_order, sort_by)
    # else:
    #     order = pd.concat([order, cancel_order], ignore_index=True, sort=False)
    #     if sort_by:
    #         order.sort_values(sort_by, inplace=True)
    order['DealID'] = order.OrderID
    order = pd.concat([cancel_order, order]).sort_values("DealID")
    order.drop("DealID", axis=1, inplace=True)
    order.reset_index(drop=True, inplace=True)

    # 从 deal 中去除撤单
    deal = deal[deal.Side != 4].reset_index(drop=True)

    return deal, order


def convert_sh_deal_order(deal, order, sort_by=['Time', 'OrderID', 'OrderType'], fast_sort=True):
    """转换上交所逐笔数据到统一格式"""
    # 还原逐笔委托的委托量(上交所连续竞价期间发布的委托量是主动成交后剩余的委托量)
    dorderbuy = deal[(deal.Side == 0)].groupby(['BuyOrderID']).agg({
        'Time': 'first', 'UpdateTime': 'first', 'Price': 'max', 'Volume': 'sum'
    })
    dorderbuy['Side'] = 0
    dordersell = deal[(deal.Side == 1)].groupby(['SaleOrderID']).agg({
        'Time': 'first', 'UpdateTime': 'first', 'Price': 'min', 'Volume': 'sum'
    })
    dordersell['Side'] = 1
    deal_order = pd.concat([dorderbuy, dordersell])
    deal_order['OrderType'] = 2
    deal_order['OrderID'] = deal_order.index

    dorder_mask = deal_order.index.isin(order.OrderID)
    dorder_ids = deal_order[dorder_mask].index
    order.index = order.OrderID.values
    order.loc[
        (order.OrderID.isin(dorder_ids)) & (order.OrderType != 5),
        'Volume'
    ] += deal_order.loc[dorder_ids, 'Volume']
    if fast_sort:
        deal_order = deal_order[~dorder_mask]
        order = merge_and_sort_order_deal(order, deal_order, sort_by)
    else:
        order = pd.concat(
            [order, deal_order[~dorder_mask]],
            ignore_index=True,
            sort=False
        )
        if sort_by:
            order.sort_values(sort_by, inplace=True)

    order.reset_index(drop=True, inplace=True)

    # 将集合竞价产生的买卖方向为 10 的成交, 按照深交所的方式进行还原
    #   BuyOrderID > SaleOrderID --> Side=0
    #   BuyOrderID < SaleOrderID --> Side=1
    unknown_side_deal = deal[deal.Side == 10]
    deal.loc[unknown_side_deal.index, 'Side'] = (
        unknown_side_deal.BuyOrderID < unknown_side_deal.SaleOrderID
    ).astype(int)

    return deal, order


def convert_deal_order(deal, order, code, inplace=False,
                       sort_by=['Time', 'OrderID', 'OrderType'], fast_sort=False):
    """统一沪深交易所的deal和order数据

    参数:
        deal : 逐笔成交的原始dataframe
        order : 逐笔委托的原始dataframe
        code : 标的代码(主要为了区分沪深交易所)
        inplace: 是否在原始上修改。默认为 False，返回新的 deal 和 order
        sort_by: 指定用于对转化后的 order 进行排序的字段，为 None 时不排序，
            默认按 Time, OrderID, OrderType 三个排序

    返回: 分别返回转换后的 deal 和 order 的 DataFrame
        deal :
            DealID: 成交ID，仅为一个行情记录号，顺序递增
            OrderID: 交易所原始订单号
            Time: 成交时间戳，精确到微秒,建议使用如下的方式转化时间：
                import ft_common_base
                df['Time'] = ft_common_base.ts2dt(df.Time)
            UpdateTime: 更新时间戳（接收到行情的时间，精确到微秒）
            SaleOrderID: 卖方委托索引
            BuyOrderID: 买方委托索引
            Side: 买卖方向; 0: 主动买 1: 主动卖 (数据剔除了4, 并对10进行划分)
            Price: 成交价格
            Volume: 成交数量
            Money: 成交金额
        order :
            OrderID: 交易所原始订单号
            Time: 委托单时间戳，精确到微秒,建议使用如下的方式转化时间：
                import ft_common_base
                df['Time'] = ft_common_base.ts2dt(df.Time)
            UpdateTime: 更新时间戳（接收到行情的时间，精确到微秒）
            Side: 买卖方向  0: 委托买入  1: 委托卖出  2: 借入  3: 出借  4: 未知类型
            Price: 委托价格,注意深交所市价单(OrderType.isin([1,3]))的委托价为0
            Volume: 委托数量 (对上交所的委托量做了还原处理,原始的是主动成交后剩余的委托量)
            OrderType: 订单类型 1: 市价单(仅深交所) 2: 限价单(上交所不区分是否市价单,统一为2)
                               3: 本方最优先(仅深交所) 5: 撤单

    返回对比 :
        deal的Side字段有变动,买卖方向,含 0: 主动买, 1: 主动卖
            相比原始deal剔除了Side=4的数据(撤单，仅限深交所,挪到了order中)
            对Side=10(未知，上交所集合竞价发送)的按照订单ID划分为0/1,
            其他字段无变动
        order的OrderType字段有变动, 订单类型
                1: 市价单(仅深交所)
                2: 限价单(上交所不区分是否市价单,统一为2)
                3: 本方最优先(仅深交所)
                5: 沪深交易所撤单
            相比原始order将深交所撤单进行了并入, 并还原补充了上交所的委托量, 其他字段无变动

    统一了的 :
    1. 将深交所撤单信息由deal中挪到order(深交所的撤单信息发布于deal,上交所发布于order)
    2. 将上交所逐笔委托的委托量进行了还原处理(上交所连续竞价期间发布的委托量是主动成交后剩余的委托量)
    3. 上交所集合竞价产生的买卖方向为10的,按照深交所的方式进行还原 :
        BuyOrderID > SaleOrderID --> Side=0
        BuyOrderID < SaleOrderID --> Side=1
    还有区别的 :
    1. 深交所市价单的委托价为 0, 可使用 fill_szmarket_price 进行填充
    2. 上交所没有区分市价单/限价单, OrderType 统一为限价单
    """
    if not inplace:
        order = order.copy()
        deal = deal.copy()
    if code.endswith("XSHE"):
        return convert_sz_deal_order(deal, order, sort_by=sort_by, fast_sort=fast_sort)
    elif code.endswith("XSHG"):
        return convert_sh_deal_order(deal, order, sort_by=sort_by, fast_sort=fast_sort)
    else:
        raise ValueError("only XSHE and XSHG securities are supported")


# 用前收价填充 deal
@lru_cache(None)
def get_fill_deal(code, date):
    deal = pd.DataFrame(
        np.full((1, len(Level2Deal.DEAL_COLUMNS)), np.nan),
        columns=Level2Deal.DEAL_COLUMNS
    )
    deal[["DealID", "SaleOrderID", "BuyOrderID"]] = 0
    # 仅在传递了 code 时才取前收价，否则用 nan 填充
    if code:
        # 前收价需要取当天的 pre_close 字段，因为要考虑除权
        pre_close = import_module("jqdata.apis").get_price(
            code, end_date=date, count=1, fq=None, fields='pre_close'
        ).iloc[-1].pre_close
        deal['Price'] = pre_close
    return deal


def get_fill_price(order, deal_a, deal_b, code, field):
    if order.empty:
        return np.array([], dtype=np.float64)

    date = pd.to_datetime(order.Time.iloc[-1] + 8 * 3600, unit='s').date()
    if deal_a.empty:
        deal_a = get_fill_deal(code, date) if deal_b.empty else deal_b
    if order.iloc[0].OrderID < deal_a.iloc[0][field]:
        pre_fill_deal = deal_b[deal_b.DealID <= order.iloc[0].OrderID]
        if pre_fill_deal.empty:
            pre_fill_deal = get_fill_deal(code, date)
        else:
            pre_fill_deal = pre_fill_deal.iloc[[-1]]
        deal_a = pd.concat((pre_fill_deal, deal_a))
    price_index = np.searchsorted(deal_a[field], order.OrderID, side='right')
    price_index[price_index > 0] -= 1
    return deal_a.Price.iloc[price_index].values


def fill_szmarket_price(deal, order, fill_type='last', code=None, inplace=False):
    """填充深交所的市价单的委托价
    填充条件 (OrderType.isin([1,3])&(Price=0)), 只对深交所有效, 注意这个还原并不是完全准确的

    参数：
        deal/order : deal/order 的 dataframe
            可以使用 convert_deal_order 格式化后的数据, 也可以使用原始的数据
        fill_type : 可选 group(注意可能有未来数据) 和 last
            last: 使用下单时主动/买卖的成交价填充(即成即撤的委托类型委托价超出了这个价格,暂时无法考虑到)
                OrderType = 1:(对手方最优)
                    委买填充价 = deal[(deal.BuyOrderID<=order.OrderID)&(deal.Side==0)].Price.last  # 下单时主动买 (对手最优) 的成交价
                    委卖填充价 = deal[(deal.SaleOrderId<=order.OrderID)&(deal.Side==1)].Price.last  # 下单时主动卖 (对手最优) 的成交价
                OrderType = 3:
                    委买填充价 = deal[(deal.DealID<=order.OrderID)&(deal.Side==1)].Price.last  # 下单时主动卖 (本方最优) 的成交价
                    委卖填充价 = deal[(deal.DealID<=order.OrderID)&(deal.Side==0)].Price.last  # 下单时主动买 (本方最优) 的成交价
            group: 使用本笔订单的最高/最低成交价填充委托价
                由于对手方最优和本方最优可能存在挂单,并不是即时成交,所以需要注意避免未来
                (传入的order/deal过滤掉指定时间以前的数据)
                委买填充价 =  deal.groupby(MarketorderID).Price.max # 最高成交价
                委卖填充价 =  deal.groupby(MarketorderID).Price.min # 最低成交价
            如果需要尽可能多的使用更多信息, 也可以分别获取一次 last 和 group ,而后取其最高价/最低价
                委买填充价 = max( group价 , last价)
                委卖填充价 = min( group价 , last价)
        code: 标的代码，仅用于在取不到填充价时获取前收价填充
        inplace: 是否在原始 order 上修改。默认为 False，返回一个新的 order

    返回 :
        市价单填充后的 order, 其中 OrderType.isin([1,3])&(Price=0) 的数据被填充,
        当fill_type=group时,可能产生无法填充的情况,以Nan进行填充(比如市价本方最优下单后没有成交直接挂起的)

    备注 :
        深交所市价单委托方式(两种转限,三种即成即撤),除本方最优可以用orderType=3区分, 其他类型的orderType都为1无法区分 :
        （一）对手方最优价格申报(剩余挂单委托)；
        （二）本方最优价格申报(剩余挂单委托)； orderType =3
        （三）最优五档即时成交剩余撤销申报；
        （四）即时(对手方所有存量委托)成交剩余撤销申报；
        （五）全额(对手方所有存量委托)成交或撤销申报；
    """
    if code and code.endswith("XSHG"):
        return order

    if fill_type == 'last':
        if not inplace:
            order = order.copy()

        order_price = order.Price.values
        price_0_mask = order_price == 0
        process_order = order[price_0_mask].copy()
        if process_order.empty:
            return order

        deal_side = deal.Side.values
        deal_side_0_mask = deal_side == 0
        deal_side_1_mask = deal_side == 1
        deal0 = deal.loc[deal_side_0_mask]  # 主动买
        deal1 = deal.loc[deal_side_1_mask]  # 主动卖

        order_side = process_order.Side.values
        side_0_mask = order_side == 0
        side_1_mask = order_side == 1
        order_type = process_order.OrderType.values
        type_1_mask = order_type == 1
        type_3_mask = order_type == 3

        # 下单时主动买 (对手最优) 的成交价
        mask = side_0_mask & type_1_mask
        order1buy = process_order.loc[mask]
        process_order.loc[mask, "Price"] = get_fill_price(order1buy, deal0, deal1, code, 'BuyOrderID')
        # 下单时主动卖 (对手最优) 的成交价
        mask = side_1_mask & type_1_mask
        order1sell = process_order.loc[mask]
        process_order.loc[mask, "Price"] = get_fill_price(order1sell, deal1, deal0, code, 'SaleOrderID')

        # 下单时主动买 (本方最优) 的成交价
        mask = side_0_mask & type_3_mask
        order3buy = process_order.loc[mask]
        process_order.loc[mask, "Price"] = get_fill_price(order3buy, deal1, deal0, code, 'DealID')
        # 下单时主动卖 (本方最优) 的成交价
        mask = side_1_mask & type_3_mask
        order3sell = process_order.loc[mask]
        process_order.loc[mask, "Price"] = get_fill_price(order3sell, deal0, deal1, code, 'DealID')

        order.loc[price_0_mask, 'Price'] = process_order['Price'].values

        # 市价单可能产生撤单，价格为 0，需要填充
        filled_price_0_mask = order.Price.values == 0
        normal_order = order.loc[order['OrderType'].values != 5]

        if is_sorted(normal_order.OrderID.values):
            no_price_order_ids = order.loc[filled_price_0_mask, 'OrderID'].values
            if len(no_price_order_ids) and not normal_order.empty:
                positions = np.searchsorted(normal_order['OrderID'], no_price_order_ids)
                # 处理 越界 positions
                positions = np.minimum(positions, normal_order.shape[0] - 1)
                order.loc[filled_price_0_mask, 'Price'] = np.where(
                    # 只替换 找到的 order_id, 没找到的置为0
                    normal_order['OrderID'].iloc[positions] == no_price_order_ids,
                    normal_order['Price'].iloc[positions].values,
                    0
                )
        else:
            no_price_order = order[filled_price_0_mask]
            order_mask = np.in1d(order['OrderID'], no_price_order['OrderID'])
            prev_price = order.loc[order_mask & (~filled_price_0_mask)].set_index("OrderID").Price
            if not prev_price.empty:
                prev_price = no_price_order.OrderID.map(prev_price)
                order.loc[no_price_order.index, 'Price'] = prev_price
        return order
    elif fill_type == "group":
        if not inplace:
            order = order.copy()
        order13 = order[order.OrderType.isin([1, 3]) & (order.Price == 0)]
        dordersell = deal[
            deal.SaleOrderID.isin(order13.OrderID) & (deal.Side != 4)
        ].groupby('SaleOrderID').Price.min()
        dorderbuy = deal[
            deal.BuyOrderID.isin(order13.OrderID) & (deal.Side != 4)
        ].groupby('BuyOrderID').Price.max()
        dorder_price = pd.concat([dordersell, dorderbuy])
        order.index = order.OrderID
        order.loc[order.index.isin(dorder_price.index), 'Price'] = dorder_price
        order.reset_index(drop=True, inplace=True)
        return order
    else:
        raise ValueError('invalid fill_type %r' % fill_type)


def get_deal_cumsum(date, fields='close', codes=None, fill_price=False, fq_ref_date=None):
    """获取使用逐笔成交数合成的1s的量价数据切片

    参数：
        date : 日期
        fields : 字段；可以为一个字符串，表示取单个字段数据；或者为列表，表示取多个字段数据；
            目前仅支持 close，volume， money 三个字段，默认为 close
        codes : 标的代码，可以为一个字符串，表示取单个标的数据；或者为列表，表示取多个标的数据；
            默认为 None, 表示取当天在市的所有标的数据(停牌标的价格全为 nan, 量额全为 0)
        fill_price : 当停牌或者当天截至某个时刻还未产生任何交易时, 是否按前收价填充,
            默认不填充, 为 nan
        fq_ref_date : 除权基准日, 只对 close 和 volume 有效, 默认为 None 不复权,
            指定 2005-01-01 或更早时间为后复权

    返回：一个 Panel, items: fields, major_axis: times, minor_axis: codes
    """
    date = pd.to_datetime(date).date()

    if isinstance(fields, str):
        fields = [fields]
    elif not isinstance(fields, import_module("collections.abc").Sequence):
        raise ValueError("fields 参数必须是一个字符串或者列表")

    if isinstance(codes, str):
        codes = [codes]
    if isinstance(codes, import_module("collections.abc").Sequence):
        codes = ['time'] + list(codes)
    elif codes is not None:
        raise ValueError("codes 参数必须是一个标的代码或者列表")

    data_rootdir = "/opt/data/jq/bundle/fastjqdata/deal_cumsum/stocks"
    data_datedir = os.path.join(data_rootdir, date.strftime("%Y%m/%Y-%m-%d"))

    dfs = {}
    for field in fields:
        path = os.path.join(data_datedir, f"{field}.fea")
        df = read_feather(path, columns=codes)
        df.set_index("time", inplace=True)
        if fill_price and field == 'close':
            need_fill_codes = df.columns[df.iloc[0].isna()].tolist()
            if need_fill_codes:
                prices = import_module("jqdata.apis").get_price(
                    need_fill_codes, end_date=date, count=1, fields='pre_close',
                    fq=None, panel=False
                ).set_index("code").pre_close
                df.loc[df.index[0], need_fill_codes] = prices
                df.loc[:, need_fill_codes] = df.loc[:, need_fill_codes].ffill()
        if fq_ref_date and field in ('volume', 'close'):
            factors = import_module("jqdata.apis").get_price(
                df.columns.tolist(),
                end_date=date, count=1, fields='factor', fq='post',
                pre_factor_ref_date=fq_ref_date,
                panel=False, round=False,
            ).set_index("code").factor
            if field == 'close':
                df = df.mul(factors)
            else:
                df = df.div(factors)
        dfs[field] = df

    pn = import_module("simple_panel").SimplePanel(dfs)
    return pn


@cache_with_ignore(
    maxsize=get_per_factor_process_max_tasks(default=1),
    ignore=[3, "fast_sort"]
)
def _load_converted_deal_order(code, date, sort_by=None, fast_sort=False,
                               fill_sz=False):
    """加载转化为统一格式后的 deal 和 order 数据"""
    order = load_order(code, date, unified=False)
    deal = load_deal(code, date, unified=False)
    kwargs = dict(inplace=True, fast_sort=fast_sort)
    if sort_by:
        kwargs['sort_by'] = sort_by
    deal, order = convert_deal_order(deal, order, code, **kwargs)
    if fill_sz and code.endswith("XSHE"):
        order = fill_szmarket_price(
            deal, order, fill_type='last', code=code, inplace=True
        )
    return deal, order


def load_deal_order(code, date, unified=False):
    """同时加载指定标的指定日期的 deal 和 order 数据"""
    deal = load_deal(code, date, unified=unified)
    order = load_order(code, date, unified=unified)
    return deal, order
